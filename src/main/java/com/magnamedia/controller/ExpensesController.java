package com.magnamedia.controller;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.NoPermission;
import com.magnamedia.core.controller.BaseRepositoryController;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.Aggregate;
import com.magnamedia.core.helper.AggregateQuery;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.ExpenseSearchDto;
import com.magnamedia.entity.projection.ExpenseCSVProjection;
import com.magnamedia.entity.projection.ExpenseProjection;
import com.magnamedia.entity.projection.IdLabelNameCodeProjection;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.extra.FilterItem;
import com.magnamedia.extra.StreamsUtil;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.type.ExpenseApprovalMethod;
import com.magnamedia.module.type.ExpenseBeneficiaryType;
import com.magnamedia.module.type.LoanType;
import com.magnamedia.repository.*;
import com.magnamedia.service.QueryService;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.projection.ProjectionFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RequestMapping("/expenses")
@RestController
public class ExpensesController extends BaseRepositoryController<Expense> {

    @Autowired
    private ExpenseRepository expenseRepository;

    @Autowired
    private ExpenseRelatedToRepository expenseRelatedToRepository;

    @Autowired
    private ExpenseRelatedToTeamRepository expenseRelatedToTeamRepository;

    @Autowired
    private TransactionPostingRuleRepository transactionPostingRuleRepository;

    @Autowired
    private DisableExpensesLogRepository disableExpensesLogRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private SupplierRepository supplierRepository;

    @Autowired
    private SupplierController supplierController;

    @Override
    public BaseRepository<Expense> getRepository() {
        return expenseRepository;
    }

    @Autowired
    ProjectionFactory projectionFactory;

    @Override
    public ResponseEntity<?> createEntity(Expense e) {
        validation(e);
        super.createEntity(e);
        fillRelatedTo(e);

        return ResponseEntity.ok(e);
    }

    private void fillRelatedTo(Expense expense) {
        for (ExpenseRelatedTo relatedTo : expense.getRelatedTos()) {
            relatedTo.setExpense(expense);
            ExpenseRelatedTo saved = expenseRelatedToRepository.save(relatedTo);
            if (relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.TEAM) {
                for (ExpenseRelatedToTeam relatedToTeam : relatedTo.getTeams()) {
                    relatedToTeam.setRelatedTo(saved);
                    expenseRelatedToTeamRepository.save(relatedToTeam);
                }

            }

        }
    }

    private void updateRelatedTo(Expense expense, Expense existExpense) {
        expense.getRelatedTos().forEach(r -> r.setExpense(expense));
        List<ExpenseRelatedTo> original = new ArrayList<>();
        if (existExpense != null) {
            original.addAll(existExpense.getRelatedTos());
        }
        ArrayList<ExpenseRelatedTo> forDelete = new ArrayList<>(original);
        forDelete.removeAll(expense.getRelatedTos());
        for (ExpenseRelatedTo delete : forDelete) {
            expenseRelatedToRepository.delete(delete.getId());
            existExpense.getRelatedTos().remove(delete);
        }

        for (ExpenseRelatedTo relatedTo : expense.getRelatedTos()) {
            if (original.contains(relatedTo)) {
                ExpenseRelatedTo exist = original.get(original.indexOf(relatedTo));
                if (exist.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.APPLICANT
                        || exist.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.OFFICE_STAFF) {
                    exist.setRelatedExpense(relatedTo.getRelatedExpense());
                } else if (exist.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.MAID) {
                    exist.setExpenseCC(relatedTo.getExpenseCC());
                    exist.setExpenseVisa(relatedTo.getExpenseVisa());
                } else if (exist.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.TEAM) {
                    List<PicklistItem> teams = relatedTo.getTeams().stream().map(ExpenseRelatedToTeam::getTeam).collect(Collectors.toList());
                    ArrayList<ExpenseRelatedToTeam> expenseRelatedToTeams = new ArrayList<>(relatedTo.getTeams());
                    expenseRelatedToTeamRepository.delete(exist.getTeams());
                    exist.getTeams().clear();
//                    exist.setTeams(relatedTo.getTeams());
                    expenseRelatedToRepository.save(exist);
                    for (ExpenseRelatedToTeam relatedTeam : expenseRelatedToTeams) {
                        ExpenseRelatedToTeam newTeam = new ExpenseRelatedToTeam();
                        newTeam.setTeam(getItem(relatedTeam.getTeam().getId()));
                        newTeam.setRelatedTo(exist);
                        Expense one = getRepository().findOne(relatedTeam.getRelatedExpense().getId());
                        if (one == null)
                            throw new RuntimeException("can not find expense with id=" + relatedTeam.getRelatedExpense().getId());
                        newTeam.setRelatedExpense(one);
                        newTeam = expenseRelatedToTeamRepository.save(newTeam);
                        exist.getTeams().add(newTeam);
                    }
                }
                expenseRelatedToRepository.save(exist);
            } else {
                relatedTo.setExpense(existExpense);
                ExpenseRelatedTo saved = expenseRelatedToRepository.save(relatedTo);
                if (relatedTo.getRelatedToType() == ExpenseRelatedTo.ExpenseRelatedToType.TEAM) {
                    for (ExpenseRelatedToTeam relatedToTeam : relatedTo.getTeams()) {
                        relatedToTeam.setRelatedTo(saved);
                        expenseRelatedToTeamRepository.save(relatedToTeam);
                    }
                }
            }
        }
        existExpense = expenseRepository.save(existExpense);
    }

    @Override
    public ResponseEntity<?> updateEntity(Expense e) {
        return okResponse();
    }

    @NoPermission
    @Transactional
    @RequestMapping(value = "/add-supplier/{expense-id}", method = RequestMethod.POST)
    public ResponseEntity addSupplier(@PathVariable("expense-id") Expense expense,
                                      @RequestBody Supplier supplier) {
        supplier = (Supplier) supplierController.create(supplier).getBody();

        if (expense.getSuppliers() == null || expense.getSuppliers().isEmpty()) return ResponseEntity.ok(supplier);

        expense.getSuppliers().add(supplier);
        expenseRepository.save(expense);

        return ResponseEntity.ok(supplier);
    }

    private void updateSuppliers(Expense expense, Expense existExpense) {
        existExpense = getRepository().getOne(existExpense.getId());
        List<Supplier> originalSuppliers = existExpense.getSuppliers();
        ArrayList<Supplier> forDelete = new ArrayList<>(originalSuppliers);
        forDelete.removeAll(expense.getSuppliers());
        for (Supplier s : forDelete) {
            s.getExpenses().remove(existExpense);
            existExpense.getSuppliers().remove(s);
        }
        for (Supplier supplier : expense.getSuppliers()) {
            Supplier one = supplierRepository.findOne(supplier.getId());
            if (!existExpense.getSuppliers().contains(one)) {
                one.getExpenses().add(expense);
                existExpense.getSuppliers().add(one);
                supplierRepository.save(one);
            }
        }
        expenseRepository.save(existExpense);

    }

    @Transactional
    @Override
    public ResponseEntity<?> update(@RequestBody ObjectNode objectNode) throws IOException {
        Expense parse = this.parse(objectNode);
//        updateEntity(parse);

        validation(parse);
        List<ExpenseRelatedTo> relatedTos = parse.getRelatedTos();

        // ACC-8884
        if (ExpenseApprovalMethod.AUTO_APPROVED.equals(parse.getApprovalMethod())) {
            parse.setApproveHolderType(null);
            parse.setApproveHolder(null);
            parse.setApproveHolderEmail(null);
            parse.setLimitForApproval(null);
        }

        Expense existExpense = expenseRepository.findOne(parse.getId());
        parse.setRelatedTos(existExpense.getRelatedTos());
        super.update(existExpense, parse, objectNode);
        parse.setRelatedTos(relatedTos);
        updateRelatedTo(parse, existExpense);
        updateSuppliers(parse, existExpense);
        return okResponse();
    }

    public Expense createSubExpense(Expense parent, String name) {
        Expense expense = cloneExpense(parent);

        expense.setParent(parent);
        expense.setName(name);
        expense.setCode(getSubExpenseCode(parent));
        expense = getRepository().save(expense);
        return expense;
    }

    private Expense cloneExpense(Expense original) {
        Expense expense = new Expense();
        expense.setParent(original.getParent());
        expense.setCode(original.getCode());
        expense.setName(original.getName());
        expense.setDisabled(original.getDisabled());
        expense.setDeleted(original.getDeleted());
        expense.setAdjustedExpense(original.getAdjustedExpense());
        expense.setAllowSubExpense(original.getAllowSubExpense());
        expense.setAllowToAddLoan(original.getAllowToAddLoan());
        expense.setApprovalMethod(original.getApprovalMethod());
        expense.setApproveHolder(original.getApproveHolder());
        expense.setApproveHolderEmail(original.getApproveHolderEmail());
        expense.setApproveHolderType(original.getApproveHolderType());
        expense.setAutoDeducted(original.getAutoDeducted());
        expense.setBeneficiaryType(original.getBeneficiaryType());
        expense.setCaption(original.getCaption());
        expense.setCodeLabel(original.getCodeLabel());
        expense.setDefaultAmount(original.getDefaultAmount());
        expense.setIsLimitedCOO(original.getIsLimitedCOO());
        expense.setIsSecure(original.getIsSecure());
        expense.setLimitCOO(original.getLimitCOO());
        expense.setLimitedCOO(original.getLimitedCOO());
        expense.setLimitForApproval(original.getLimitForApproval());
        expense.setLoanType(original.getLoanType());
        expense.setManager(original.getManager());
        expense.setNameLabel(original.getNameLabel());
        expense.setPnlExpenseType(original.getPnlExpenseType());
        expense.setRequestedFrom(original.getRequestedFrom());
        expense.setRequireAttachment(original.getRequireAttachment());
        expense.setRequireInvoice(original.getRequireInvoice());
        expense.setSalaryAdditionType(original.getSalaryAdditionType());
        expense.setSubExpenseLabel(original.getSubExpenseLabel());

        expense.setPaymentMethods(original.getPaymentMethods() != null && !original.getPaymentMethods().isEmpty() ? new HashSet(original.getPaymentMethods()) : new HashSet());
        expense.setRequestors(original.getRequestors() != null && !original.getRequestors().isEmpty() ? new ArrayList(original.getRequestors()) : new ArrayList());
        expense.setSuppliers(original.getSuppliers() != null && !original.getSuppliers().isEmpty() ? new ArrayList(original.getSuppliers()) : new ArrayList());
        expense.setNamesInFinancialStatements(original.getNamesInFinancialStatements() != null && !original.getNamesInFinancialStatements().isEmpty() ? new ArrayList(original.getNamesInFinancialStatements()) : new ArrayList());
        //notification
        ExpenseNotification originalNotification = original.getNotification();
        if (originalNotification != null) {
            ExpenseNotification notification = new ExpenseNotification();
            notification.setAmountEnabled(originalNotification.isAmountEnabled());
            notification.setAmount(originalNotification.getAmount());
            notification.setAmountOperation(originalNotification.getAmountOperation());
            notification.setExpense(expense);
            notification.setNotifyUsers(originalNotification.getNotifyUsers() != null && !originalNotification.getNotifyUsers().isEmpty() ? new ArrayList(originalNotification.getNotifyUsers()) : new ArrayList());
            notification.setNumberOfRequestsEnabled(originalNotification.isNumberOfRequestsEnabled());
            notification.setNumberOfRquestComparedTo(originalNotification.getNumberOfRquestComparedTo());
            notification.setPercentage(originalNotification.getPercentage());
            notification.setPercentageComparedTo(originalNotification.getPercentageComparedTo());
            notification.setPercentageEnabled(originalNotification.isPercentageEnabled());
            notification.setPercentageNOCompared(originalNotification.getPercentageNOCompared());
            notification.setPercentageOperation(originalNotification.getPercentageOperation());

            expense.setNotification(notification);
        }

        expense = getRepository().save(expense);
        //related to
        for (ExpenseRelatedTo originalRelatedTo : original.getRelatedTos()) {
            ExpenseRelatedTo relatedTo = new ExpenseRelatedTo();
            relatedTo.setExpense(expense);
            relatedTo.setExpenseCC(originalRelatedTo.getExpenseCC());
            relatedTo.setExpenseVisa(originalRelatedTo.getExpenseVisa());
            relatedTo.setRelatedToType(originalRelatedTo.getRelatedToType());
            relatedTo.setRelatedExpense(originalRelatedTo.getRelatedExpense());
            relatedTo = expenseRelatedToRepository.save(relatedTo);

            for (ExpenseRelatedToTeam originalTeam : originalRelatedTo.getTeams()) {
                ExpenseRelatedToTeam team = new ExpenseRelatedToTeam();
                team.setRelatedTo(relatedTo);
                team.setTeam(originalTeam.getTeam());
                team.setRelatedExpense(originalTeam.getRelatedExpense());
                team = expenseRelatedToTeamRepository.save(team);
                relatedTo.getTeams().add(team);
            }
            expense.getRelatedTos().add(relatedTo);
        }

        return expense;
    }

    public String getSubExpenseCode(Expense parent) {
        List<String> children = expenseRepository.findAllCodeChildByParent(parent.getId());

        if (children.isEmpty()) {
            return parent.getCode() + "-" + String.format("%04d", 1);
        }

        children.sort(Comparator.comparingInt(c -> Integer.parseInt(c.substring(c.lastIndexOf("-") + 1))));

        String expenseCode = children.get(children.size() - 1);
        String code = expenseCode.substring(expenseCode.lastIndexOf("-") + 1);
        logger.info("last code: " + code);

        int number = Integer.parseInt(code);
        return parent.getCode() + "-" + String.format("%04d", number + 1);
    }

    @PreAuthorize("hasPermission('expenses','getByName')")
    @RequestMapping(path = "/getByName",
            method = RequestMethod.GET)
    public ResponseEntity<?> getByName(@RequestParam String name) {
        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);
        query.filterBy(
                new SelectFilter("name", "like", "%" + name + "%")
        );
        List<Expense> result = query.execute();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @NoPermission
    @RequestMapping(path = "/getByCode",
            method = RequestMethod.GET)
    public ResponseEntity<?> getByCode(
            @RequestParam String code,
            @RequestParam(name = "like", required = false, defaultValue = "false") boolean like) {
        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);
        query.filterBy(
                like ? new SelectFilter("code", "like", "%" + code + "%") : new SelectFilter("code", "=", code)
        );
        List<Expense> result = query.execute();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expenses','getExpenseRelatedToSupplier')")
    @GetMapping(path = "/getExpenseRelatedToSupplier")
    public ResponseEntity<?> getExpenseRelatedToSupplier(@RequestParam String search, Pageable pageable) {
        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);
        query.filterBy(new SelectFilter("name", "like", "%" + search + "%")
                .or("code", "like", "%" + search + "%")
                .or("caption", "like", "%" + search + "%"));
        query.filterBy("deleted", "=", false);
        query.filterBy("disabled", "=", false);
        query.filterBy("beneficiaryType", "=", ExpenseBeneficiaryType.SUPPLIER);

        Page<Expense> result = query.execute(pageable);
        return ResponseEntity.ok(project(result, IdLabelNameCodeProjection.class));
    }

    private void validation(Expense e) {
        String code = e.getCode();
        String name = e.getName();
        if (e.getId() != null) {
            if (code == null || code.isEmpty() || expenseRepository.existsBeforeByCode(e.getId(), code)) {
                throw new RuntimeException("code should be non empty / unique, " + code);
            }

            if (name == null || name.isEmpty() || expenseRepository.existsBeforeByName(e.getId(), name)) {
                throw new RuntimeException("name should be non empty / unique, " + name);
            }
        } else {
            if (code == null || code.isEmpty() || expenseRepository.existsByCode(code)) {
                throw new RuntimeException("code should be non empty / unique, " + code);
            }

            if (name == null || name.isEmpty() || expenseRepository.existsByName(name)) {
                throw new RuntimeException("name should be non empty / unique, " + name);
            }
        }

        if (e.isAllowToAddSupplierToExpenseRequest()) {
            if (e.getBeneficiaryType() == null || !e.getBeneficiaryType().equals(ExpenseBeneficiaryType.SUPPLIER)) {
                throw new RuntimeException("Beneficiary Type must be " + ExpenseBeneficiaryType.SUPPLIER);
            }

            if (e.getSuppliers() != null && e.getSuppliers().size() == 1) {
                throw new RuntimeException("You are allowed to choose no supplier, or more than one supplier");
            }
        }

        if (BooleanUtils.toBoolean(e.getAllowToAddLoan())) {
            if (e.getLoanType() == null) {
                throw new RuntimeException("You must choose a Loan Type");
            }
        }

        if (e.getParent() != null) {
            Expense parent = expenseRepository.findOne(e.getParent().getId());
            if (parent.getParent() != null)
                throw new RuntimeException("parent expense can not be sub-expense");
        }

        if ((e.getAutoDeducted() != null &&
                e.getAutoDeducted()) &&
                (e.getBeneficiaryType() == null ||
                        e.getBeneficiaryType() != ExpenseBeneficiaryType.SUPPLIER ||
                        e.getSuppliers() == null ||
                        e.getSuppliers().size() != 1)) {
            throw new RuntimeException("for auto deducted expense Benefeciary should be only one supplier");

        }

    }

    @NoPermission
    @RequestMapping(path = "/getSubExpenseCode/{id}",
            method = RequestMethod.GET)
    public ResponseEntity getSubExpenseCode(@PathVariable Long id) {
        Expense expense = getRepository().findOne(id);
        if (expense == null)
            throw new RuntimeException("can not find expense with id=" + id);
        String code = getSubExpenseCode(expense);
        return new ResponseEntity(code, HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expenses','page/searchExpenses/csv')")
    @RequestMapping(path = "/page/searchExpenses/csv",
            method = RequestMethod.GET)
    public void downloadAttachment(
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "withDeleted", required = false, defaultValue = "false") boolean withDeleted,
            @RequestParam(name = "activeFilter", required = false, defaultValue = "enabled") String activeFilter,
            @RequestParam(name = "onlyParent", required = false, defaultValue = "false") boolean onlyParent,
            HttpServletResponse response) throws Exception {

        SelectQuery<Expense> query = new SelectQuery(Expense.class);
        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("name", "like", "%" + queryString + "%")
                            .or("code", "like", "%" + queryString + "%")
                            .or("caption", "like", "%" + queryString + "%"));
        }

        if (!withDeleted) {
            query.filterBy("deleted", "=", false);
        }
        if (onlyParent) {
            query.filterBy("parent", "IS NULL", null);
        }

        //filter on activeFilter field
        if (activeFilter.equals("enabled"))
            query.filterBy("disabled", "=", false);
        else if (activeFilter.equals("disabled"))
            query.filterBy("disabled", "=", true);
        //else if (activeFilter.equals("all")) don't filter on anything

        String[] namesOrdared = {"name", "code", "withinPnLExpenseTypeCode", "annualPayment"};
        InputStream is = null;
        
        try {
            is = generateCsv(query, ExpenseCSVProjection.class, namesOrdared, 1000);
            createDownloadResponse(response, "test.csv", is);
        } finally {
            StreamsUtil.closeStream(is);
        }
    }

    @PreAuthorize("hasPermission('expenses','page/searchExpenses')")
    @RequestMapping("/page/searchExpenses")
    public ResponseEntity<?> searchExpenses(
            Pageable pageable,
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "withStats", required = false, defaultValue = "false") Boolean withStats,
            @RequestParam(name = "withDeleted", required = false, defaultValue = "false") boolean withDeleted,
            @RequestParam(name = "activeFilter", required = false, defaultValue = "enabled") String activeFilter,
            @RequestParam(name = "transactionId", required = false) Long transactionId,
            @RequestParam(name = "onlyParent", required = false, defaultValue = "false") Boolean onlyParent) {

        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);

        if (queryString != null && !queryString.isEmpty()) {
            query.filterBy(
                    new SelectFilter("name", "like", "%" + queryString + "%")
                                        .or("code", "like", "%" + queryString + "%")
                                        .or("caption", "like", "%" + queryString + "%"));
        }

        if (!withDeleted) {
            query.filterBy("deleted", "=", false);
        }
        if (onlyParent) {
            query.filterBy("parent", "IS NULL", null);
        }

        // if transactionId is not null then don't filter on disabled field
        if (transactionId != null) {
            Transaction transaction = transactionRepository.getOne(transactionId);
            if (transaction != null) {
                List<Long> disabledExpensesIds = disableExpensesLogRepository.getDisabledExpenses(transaction.getCreationDate());
                if (disabledExpensesIds.size() > 0)
                    query.filterBy("id", "NOT IN", disabledExpensesIds);
            }
        } else if (activeFilter != null) { // else filter on activeFilter field
            if (activeFilter.equals("enabled"))
                query.filterBy("disabled", "=", false);
            else if (activeFilter.equals("disabled"))
                query.filterBy("disabled", "=", true);
        }

        //Sorting
        if (pageable.getSort() != null) {
            for (Sort.Order order : pageable.getSort()) {
                query.sortBy(order.getProperty(), order.isAscending(), !order.isAscending());
            }
        } else {
            query.sortBy("code", true, false);
        }

        Double balanceSum = 0D;
        if (withStats) {
            AggregateQuery aggQuery = new AggregateQuery(query, Aggregate.Sum, "adjustedExpense");
            balanceSum = aggQuery.execute().doubleValue();
        }

        PageImpl s = (PageImpl) query.execute(pageable);

        Page projectedResult = s.map(e -> {
            if(!withDeleted)
                ((Expense)e).getChildren().removeAll(((Expense)e).getChildren().stream()
                        .filter(ex -> ex.getDeleted())
                        .collect(Collectors.toList()));

            return projectionFactory.createProjection(ExpenseProjection.class, e);
        });

        return new ResponseEntity<>(
                new AccountingPage(projectedResult.getContent(), pageable, s.getTotalElements(), balanceSum),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expenses','searchExpensesNew')")
    @RequestMapping("/page/searchExpensesNew")
    public ResponseEntity<?> searchExpensesNew(
            Pageable pageable,
            @RequestParam(name = "search", required = false) String queryString,
            @RequestParam(name = "withDeleted", required = false, defaultValue = "false") boolean withDeleted,
            @RequestParam(name = "activeFilter", required = false, defaultValue = "enabled") String activeFilter,
            @RequestParam(name = "onlyParent", required = false, defaultValue = "false") Boolean onlyParent,
            @RequestBody(required = false) List<FilterItem> filters) {

        return new ResponseEntity<>(
                Setup.getApplicationContext().getBean(QueryService.class)
                        .expenseSearch(pageable, queryString, withDeleted,
                                        activeFilter, onlyParent, filters),
                HttpStatus.OK);
    }

    @PreAuthorize("hasPermission('expenses','enableDisableExpense')")
    @RequestMapping("/enableDisableExpense/{id}")
    public ResponseEntity<?> enableDisableExpense(
            @PathVariable("id") Expense expense,
            @RequestParam("enable") boolean enable,
            @RequestParam("userInformed") boolean userInformed) {

        DisableExpensesLog expensesLog = disableExpensesLogRepository.findTopByExpenseOrderByCreationDateDesc(expense);

        if (enable) {
            //check the current status of the expense
            //also check if the last log record has enable date then it's already enabled
            if (!expense.getDisabled()
                    || (expensesLog != null && expensesLog.getEnableDate() != null))
                throw new RuntimeException("The Expense is already enabled");

            // ACC-2705
            if (transactionPostingRuleRepository.existsByExpenseAndActive(expense, false) && !userInformed) {
                return new ResponseEntity(0, HttpStatus.BAD_REQUEST);
            }

            expense.setDisabled(false);
            expenseRepository.save(expense);

            if (expensesLog != null) {
                expensesLog.setEnableDate(new Date());
                disableExpensesLogRepository.save(expensesLog);
            }
        } else { // disable scenario here
            //check the current status of the expense
            //also check if the last log record doesn't have enable date then it's already disabled
            if (expense.getDisabled()
                    || (expensesLog != null && expensesLog.getEnableDate() == null))
                throw new RuntimeException("The Expense is already disabled");

            // ACC-2705
            if (transactionPostingRuleRepository.existsByExpenseAndActive(expense, true)) {
                return new ResponseEntity(0, HttpStatus.BAD_REQUEST);
            }

            expense.setDisabled(true);
            expenseRepository.save(expense);

            //create new DisableExpenseLog
            DisableExpensesLog newExpenseLog = new DisableExpensesLog();
            newExpenseLog.setDisableDate(new Date());
            newExpenseLog.setExpense(expense);
            disableExpensesLogRepository.save(newExpenseLog);
        }

        return ResponseEntity.ok((enable ? "Enabled" : "Disabled") + " Successfully");
    }

    //Jirra ACC-2705
    @PreAuthorize("hasPermission('expenses','deleteExpense')")
    @RequestMapping(value = "/deleteExpense/{id}", method = RequestMethod.POST)
    public ResponseEntity<?> deleteExpense(@PathVariable("id") Expense expense) {

        if (expense.getDeleted())
            throw new RuntimeException("The Expense is already deleted");

        if (transactionPostingRuleRepository.existsByExpenseAndActive(expense, true)) {
            return new ResponseEntity(0, HttpStatus.BAD_REQUEST);
        }

        expense.setDeleted(true);
        expenseRepository.save(expense);

        return ResponseEntity.ok(1);
    }

    @NoPermission
    @RequestMapping(path = "/getPaymentMethodsByExpenseCode",
            method = RequestMethod.GET)
    public ResponseEntity<?> getPaymentMethodsByExpenseCode(@RequestParam String code) {
        SelectQuery<Expense> query = new SelectQuery<>(Expense.class);
        query.filterBy(
                new SelectFilter("code", "=", code)
        );
        List<Expense> result = query.execute();
        if (result == null || result.isEmpty())
            throw new RuntimeException("There is no such expense");
        else if (result.size() > 1)
            throw new RuntimeException("There is more than one expense with this code");

        return new ResponseEntity<>(result.get(0).getPaymentMethods(), HttpStatus.OK);
    }


    @PreAuthorize("hasPermission('expenses','searchExpenses')")
    @RequestMapping("/search")
    public ResponseEntity searchExpenses(@RequestBody ExpenseSearchDto search,
                                         Pageable pageable) {
        SelectQuery<Expense> query = new SelectQuery(Expense.class);

        if (search.isEnabled()) {
            query.filterBy("disabled", "=", Boolean.FALSE);
        }
        if (search.isDisabled()) {
            query.filterBy("disabled", "=", Boolean.TRUE);
        }
        if (!StringUtils.isEmpty(search.getCaption())) {
            query.filterBy("caption", "like", "%" + search.getCaption() + "%");
        }
        if (search.getRelatedToType() != null) {
            query.leftJoinFetch("relatedTos");
            query.filterBy("relatedTos.relatedToType", "=", search.getRelatedToType());
        }

        ResponseEntity response;
        if (pageable != null) {
            response = new ResponseEntity(query.execute(pageable).map(todo -> projectionFactory.createProjection(
                    ExpenseProjection.class, todo)), HttpStatus.OK);
        } else {
            response = new ResponseEntity(query.execute()
                    .stream().map(todo -> projectionFactory.createProjection(
                            ExpenseProjection.class, todo)), HttpStatus.OK);
        }

        return response;
    }

    @PreAuthorize("hasPermission('expenses','getLoanTypes')")
    @GetMapping("/getLoanTypes")
    public ResponseEntity<?> getLoanTypes() {
        List<Map<String, String>> r = new ArrayList<>();
        LoanType[] array = LoanType.values();

        for (LoanType l: array) {
            if (l.isDeprecated()) continue;

            Map<String, String> map = new HashMap<>();
            map.put("id", l.getValue());
            map.put("text", l.getLabel());
            r.add(map);
        }

        return ResponseEntity.ok(r);
    }
}
