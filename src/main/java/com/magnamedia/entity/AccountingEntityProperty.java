package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.repository.AccountingEntityPropertyRepository;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Mar 15, 2020
 *         Jirra ACC-1435
 */

@Entity
@Table(indexes = {@Index(columnList = "ORIGIN_TYPE, ORIGIN_ID")},
        uniqueConstraints = {@UniqueConstraint(columnNames = {"ORIGIN_TYPE", "ORIGIN_ID", "_KEY", "PURPOSE", "DELETED_COUNT"})
        })
public class AccountingEntityProperty extends BaseEntity {

    @Column(name = "_KEY")
    private String key;

    @Column
    @Lob
    private String value;

    @Column(name = "ORIGIN_TYPE")
    @JsonIgnore
    private String originType;

    @JsonIgnore
    @Column(name = "ORIGIN_ID")
    private Long originId;

    @JsonIgnore
    @Column
    private Date messageSendDate;

    @JsonIgnore
    @Transient
    private BaseEntity origin;

    @Column
    private String purpose;

    //Added for OEC flow
    @JsonIgnore
    @Column(name = "JOB_RUN_AND_DD_PENDING", columnDefinition = "boolean default false")
    private boolean jobRunAndDDPending;

    @JsonIgnore
    @Column(columnDefinition = "boolean default false")
    private boolean isDeleted;

    @JsonIgnore
    @Column(name = "DELETED_COUNT", columnDefinition = "int default 0")
    private int deletedCount;

    @JsonIgnore
    public BaseEntity getOrigin() {
        if (origin != null)
            return origin;
        String repositoryName = originType.toLowerCase().charAt(0) + originType.substring(1) + "Repository";
        origin = ((BaseRepository<? extends BaseEntity>) Setup.getApplicationContext().getBean(repositoryName))
                .findOne(originId);
        return origin;
    }

    @JsonIgnore
    public void setOrigin(BaseEntity entity) {
        if (entity == null) {
            this.originType = null;
            this.originId = null;
        } else {
            this.originType = entity.getEntityType();
            this.originId = entity.getId();
        }
        this.origin = entity;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public AccountingEntityProperty() {
    }

    public AccountingEntityProperty(String key, String value, BaseEntity entity) {
        this.key = key;
        this.value = value;
        setOrigin(entity);
    }

    public boolean isJobRunAndDDPending() {
        return jobRunAndDDPending;
    }

    public void setJobRunAndDDPending(boolean jobRunAndDDPending) {
        this.jobRunAndDDPending = jobRunAndDDPending;
    }

    public boolean getIsDeleted() { return isDeleted; }

    public void setIsDeleted(boolean isDeleted) {
        this.isDeleted = isDeleted;

        if (isDeleted) {
            Integer deletedCount = Setup.getRepository(AccountingEntityPropertyRepository.class)
                    .countDeletedByKeyUniqueConstraints(
                            this.getOrigin().getId(),
                            this.getOrigin().getEntityType(),
                            this.getKey());

            this.setDeletedCount(deletedCount != null ? deletedCount + 1 : 1);
        }
    }

    public int getDeletedCount() { return deletedCount; }

    public void setDeletedCount(int deletedCount) { this.deletedCount = deletedCount; }

    public Date getMessageSendDate() { return messageSendDate; }

    public void setMessageSendDate(Date messageSendDate) { this.messageSendDate = messageSendDate; }

    public String getPurpose() { return purpose; }

    public void setPurpose(String purpose) { this.purpose = purpose; }
}
