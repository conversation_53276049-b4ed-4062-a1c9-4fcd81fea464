package com.magnamedia.entity;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.serializer.TypeIdLabelSerializer;
import com.magnamedia.repository.AdhocNodeRepository;
import com.magnamedia.repository.BasePLNodeRepository;
import org.hibernate.envers.NotAudited;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@Entity
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(value = AdhocVariableNode.class, name = "AdhocVariableNode")})
@DiscriminatorColumn(name = "type")
public class AdhocNode extends BasePLNode<AdhocCompany> {

    @ManyToOne
    @JsonSerialize(using = IdLabelSerializer.class)
    @JoinColumn(name = "P_LCOMPANY_ID")    
    protected AdhocCompany PLCompany;

    @ManyToOne
    @JsonSerialize(using = TypeIdLabelSerializer.class)
    protected AdhocNode parent;

    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true, mappedBy = "parent")
    protected List<AdhocNode> children = new ArrayList();

    @Override
    public AdhocCompany getPLCompany() {
        return PLCompany != null ? PLCompany : parent != null ? parent.getPLCompany() : PLCompany;
    }

    @Override
    public void setPLCompany(AdhocCompany pLCompany) {
        this.PLCompany = pLCompany;
    }

    @Override
    public AdhocNode getParent() {
        return parent;
    }

    @Override
    public void setParent(BasePLNode parent) {
        this.parent = (AdhocNode) parent;
    }

    @Override
    public List<BasePLNode> getChildren() {
        return children.stream().map(adhocNode -> ((BasePLNode) adhocNode)).collect(Collectors.toList());
    }

    @Override
    public void setChildren(List<BasePLNode> children) {
        this.children = children.stream().map(basePLNode -> ((AdhocNode) basePLNode)).collect(Collectors.toList());
    }

    @Override
    public List<BasePLNode> getSortedChildren() {
        if (this.children != null && !this.children.isEmpty())
            return this.children.stream()
                    .sorted(Comparator.comparingInt(AdhocNode::getNodeOrder))
                    .collect(Collectors.toList());
        else
            return this.children.stream().map(plNode -> ((BasePLNode) plNode)).collect(Collectors.toList());
    }

    @Override
    public void validate() {
        if (!noValidate) {
            super.validate();
            List<BasePLNode> bros;

            if (getParent() != null)
                bros = getParent().getChildren();
            else
                bros = getPLCompany().getChildren();

            //Jirra ACC-387
            if (getId() != null)
                bros = bros.stream().filter(x -> x.getName() != null && !x.getName().isEmpty() && x.getId() != null && x.getName().equals(getName()) && ((getId() != null && !x.getId().equals(getId())) || getId() == null)).collect(Collectors.toList());
            if ((getId() != null && !bros.isEmpty()) || (getId() == null && bros.size() > 1)) {
                throw new RuntimeException("Name should be unique in the same company or parent node.");
            }
        }
    }

    @Override
    @JsonIgnore
    protected BasePLNodeRepository getRepository() {
        return Setup.getRepository(AdhocNodeRepository.class);
    }

}
