package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdSerializer;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import java.util.Date;

@Entity
public class AmwalStatementRecord extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private VisaStatement statement;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdSerializer.class)
    private VisaStatementTransaction transaction;

    @Column
    private Date transactionDate;

    @Column
    private String transactionType;

    @Column
    private String transactionNumber;

    @Column
    private String transactionStatue;

    @Column(columnDefinition = "double default 0")
    private Double oldBalance = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double availableBalance = 0.0;

    @Column(columnDefinition = "double default 0")
    private Double amount = 0.0;

    public VisaStatement getStatement() {
        return statement;
    }

    public void setStatement(VisaStatement statement) {
        this.statement = statement;
    }

    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public String getTransactionStatue() {
        return transactionStatue;
    }

    public void setTransactionStatue(String transactionStatue) {
        this.transactionStatue = transactionStatue;
    }

    public Double getOldBalance() {
        return oldBalance;
    }

    public void setOldBalance(Double oldBalance) {
        this.oldBalance = oldBalance;
    }

    public Double getAvailableBalance() {
        return availableBalance;
    }

    public void setAvailableBalance(Double availableBalance) {
        this.availableBalance = availableBalance;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public VisaStatementTransaction getTransaction() {
        return transaction;
    }

    public void setTransaction(VisaStatementTransaction transaction) {
        this.transaction = transaction;
    }
}
