package com.magnamedia.entity;

import com.magnamedia.workflow.visa.ExpensePurpose;

import javax.persistence.Entity;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Sep 17, 2017
 */
@Entity
public class CancelRequestExpense extends VisaExpense<CancelRequest> {

    public CancelRequestExpense() {
        super(null,
                null);
    }

    public CancelRequestExpense(CancelRequest request, ExpensePurpose purpose) {
        super(request, purpose);
    }

    @Override
    public String getVisaExpenseType() {
        return "CancelRequestExpense";
    }
}
