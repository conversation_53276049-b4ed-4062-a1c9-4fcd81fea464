package com.magnamedia.entity;

import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.BeforeDelete;
import com.magnamedia.core.annotation.BeforeInsert;
import com.magnamedia.core.annotation.BeforeUpdate;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.repository.AdhocCompanyRepository;
import com.magnamedia.repository.CompanyRepository;
import com.magnamedia.repository.PLCompanyRepository;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Jul 20, 2020
 *         Jirra ACC-644
 */

@Entity
public class Company extends BaseEntity {
    @Column
    @NotNull
    protected boolean isActive = true;

    @Label
    @NotNull
    protected String name;

    public boolean isIsActive() {
        return isActive;
    }

    public void setIsActive(boolean isActive) {
        this.isActive = isActive;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @BeforeInsert
    @BeforeUpdate
    public void validate() {
        CompanyRepository companyRepository = Setup.getRepository(CompanyRepository.class);
        List<Company> companies = companyRepository.findByName(getName());
        if (getId() != null)
            companies = companies.stream().filter(x -> x.getId() != getId()).collect(Collectors.toList());
        if (!companies.isEmpty()) {
            throw new RuntimeException("Name should be unique.");
        }
    }

    @BeforeDelete
    public void beforDelete() {
        //deleting all children reports
        PLCompanyRepository plCompanyRepository = Setup.getRepository(PLCompanyRepository.class);
        AdhocCompanyRepository adhocCompanyRepository = Setup.getRepository(AdhocCompanyRepository.class);

        List<BaseReportCompany> relatedReports = plCompanyRepository.findByCompany(this);

        for (BaseReportCompany reportCompany : relatedReports)
            plCompanyRepository.delete((PLCompany) reportCompany);

        relatedReports = adhocCompanyRepository.findByCompany(this);

        for (BaseReportCompany reportCompany : relatedReports)
            adhocCompanyRepository.delete((AdhocCompany) reportCompany);
    }
}
