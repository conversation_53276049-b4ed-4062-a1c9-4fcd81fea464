package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdLabelSerializer;
import org.hibernate.envers.NotAudited;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Feb 8, 2020
 *         Jirra ACC-1227
 */

@Entity
public class FreedomOperatorTransaction extends BaseEntity {
    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Transaction transaction;

    @NotAudited
    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private FreedomOperator freedomOperator;


    @Override
    public boolean equals(Object object) {
        if (!(object instanceof FreedomOperatorTransaction)) {
            return false;
        } else {
            FreedomOperatorTransaction other = (FreedomOperatorTransaction) object;
            return this.getId().equals(other.getId())
                    && this.freedomOperator.getId().equals(other.freedomOperator.getId()) && this.transaction.getId().equals(other.transaction.getId());
        }
    }

    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public FreedomOperator getFreedomOperator() {
        return freedomOperator;
    }

    public void setFreedomOperator(FreedomOperator freedomOperator) {
        this.freedomOperator = freedomOperator;
    }
}
