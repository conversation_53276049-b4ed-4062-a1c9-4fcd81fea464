package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.helper.NumberFormatter;

import javax.persistence.*;
import java.sql.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2/11/2021
 */
@Entity
public class InsuranceAuditingRecord extends BaseEntity {

    public enum recordType {
        ADDITION,
        DELETION
    }

    public enum matchType {
        DUPLICATED,
        UNMATCHED,
        MATCHED
    }

    @Column
    private Date effectiveDate;
    @Column
    private String name;
    @Column
    private String passportNum;
    @Column
    private Double amount;
    @Column
    private Double erpAmount;

    @Enumerated(EnumType.STRING)
    private recordType type;

    @Enumerated(EnumType.STRING)
    private matchType matchType;
    
    @ManyToOne
    @JsonSerialize(using = IdJsonSerializer.class)
    private InsuranceAuditingStatement statement;

    @Transient
    private Double difference;

    public recordType getType() {
        return type;
    }

    public void setType(recordType type) {
        this.type = type;
    }

    public InsuranceAuditingRecord.matchType getMatchType() {
        return matchType;
    }

    public void setMatchType(InsuranceAuditingRecord.matchType matchType) {
        this.matchType = matchType;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPassportNum() {
        return passportNum;
    }

    public void setPassportNum(String passportNum) {
        this.passportNum = passportNum;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public Double getErpAmount() {
        return erpAmount;
    }

    public void setErpAmount(Double erpAmount) {
        this.erpAmount = erpAmount;
    }

    public Double getDifference() {
        if (erpAmount == null || amount == null)
            return null;
        Double diff = amount - erpAmount;

        if (diff < 0)
            return - NumberFormatter.twoDecimalPoints(Math.abs(diff));
        else 
            return NumberFormatter.twoDecimalPoints(diff);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InsuranceAuditingRecord that = (InsuranceAuditingRecord) o;
        return Objects.equals(effectiveDate, that.effectiveDate) &&
                Objects.equals(name, that.name) &&
                Objects.equals(passportNum, that.passportNum) &&
                Objects.equals(type, that.type);
    }

    public InsuranceAuditingStatement getStatement() {
        return statement;
    }

    public void setStatement(InsuranceAuditingStatement statement) {
        this.statement = statement;
    }

    @Override
    public int hashCode() {
        return 40;
    }

}
