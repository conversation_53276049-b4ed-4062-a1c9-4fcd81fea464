package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.entity.serializer.TypeIdLabelSerializer;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 2, 2018
 */
@Entity
public class PLVariableBucket extends BasePLVariableBucket {

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = TypeIdLabelSerializer.class)
    @JoinColumn(name = "P_LVARIABLE_ID")
    @JsonIgnore
    private PLVariableNode PLVariable;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Revenue revenue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonSerialize(using = IdLabelSerializer.class)
    private Expense expense;

    @Override
    @JsonIgnore
    public BasePLVariableNode getpLVariable() {
        return PLVariable;
    }

    @Override
    public void setpLVariable(BasePLVariableNode pLVariable) {
        this.PLVariable = (PLVariableNode) pLVariable;
    }

    @Override
    public Revenue getRevenue() {
        return revenue;
    }

    @Override
    public void setRevenue(Revenue revenue) {
        this.revenue = revenue;
    }

    @Override
    public Expense getExpense() {
        return expense;
    }

    @Override
    public void setExpense(Expense expense) {
        this.expense = expense;
    }
}
