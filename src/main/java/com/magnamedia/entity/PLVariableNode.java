package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.magnamedia.controller.BaseCompanyReportController;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.module.type.PLNodeType;
import org.hibernate.envers.NotAudited;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> kanaan <<EMAIL>> Created on Oct 2, 2018
 */
@Entity
public class PLVariableNode extends PLNode implements BasePLVariableNode<PLVariableBucket> {

    @NotAudited
    @OneToMany(cascade = CascadeType.ALL, mappedBy = "PLVariable")
    private List<PLVariableBucket> pLVariableBuckets = new ArrayList<>();

    @Transient
    private String formula;

    public List<PLVariableBucket> getpLVariableBuckets() {
        return pLVariableBuckets;
    }

    @Override
    public void setpLVariableBuckets(List<PLVariableBucket> pLVariableBuckets) {
        this.pLVariableBuckets = pLVariableBuckets;
    }

    @Override
    public void setFormula(String formula) {
        this.formula = formula;
    }

    // ACC-445 3) show the code not the name in the formula | Majd Bousaad
    @Override
    public String getFormula() {
        formula = BasePLVariableNode.super.getFormula();
        return formula;
    }

    @Override
    public Double calculateAndSetValue(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
        Double result = BasePLVariableNode.super.calculateAndSetValue(fromDate, toDate);
        setValue(result);
        return result;
    }

    @Override
    public Double calculateValue(Date fromDate, Date toDate) {
        Double result = BasePLVariableNode.super.calculateAndSetValue(fromDate, toDate);
//        setValue(result);
        return result;
    }

    @Override
    public Double calculateVatAmount(Date fromDate, Date toDate) {
        Double result = BasePLVariableNode.super.calculateVatAmount(fromDate, toDate);
        setVatAmount(result);
        return result;
    }

    @Override
    public Double calculateProfitAdjustmentValue(Date fromDate, Date toDate) {
        Double result = BasePLVariableNode.super.calculateProfitAdjustmentValue(fromDate, toDate);

        //Jirra ACC-2552
        if (this.getpLNodeType().equals(PLNodeType.EXPENSES)) result = -1 * result;

        setProfitAdjustment(result);
        return result;
    }

    @Override
    public Double calculateAverageValue(Date fromDate, Date toDate) {
        Double result = BasePLVariableNode.super.calculateAverageValue(fromDate, toDate);
        setAverage(result);
        return result;
    }

    //Jirra ACC-804
    @Override
    public Double calculateOutputVATCollected(Date fromDate, Date toDate) {
        return BasePLVariableNode.super.calculateOutputVATCollected(fromDate, toDate);
    }

    //Jirra ACC-804
    @Override
    public Double calculateInputVATCollected(Date fromDate, Date toDate) {
        return BasePLVariableNode.super.calculateInputVATCollected(fromDate, toDate);
    }

    // ACC-496 2) Get all transactions behind a row in P&Ls page | Majd Bousaad
    @Override
    public List<Transaction> getTransactionsBehindNode(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
        return BasePLVariableNode.super.getTransactionsBehindNode(fromDate, toDate);
    }

    @Override
    public List<TransactionDetails> getTransactionDetailsBehindNode(Date fromDate, Date toDate, BaseCompanyReportController.SearchCriteria searchCriteria) {
        this.searchCriteria = searchCriteria;
        return BasePLVariableNode.super.getTransactionDetailsBehindNode(fromDate, toDate);
    }

    @Override
    public void validate() {
        if (!noValidate) {
            BasePLVariableNode.super.validate();
            super.validate();
        }
    }
}
