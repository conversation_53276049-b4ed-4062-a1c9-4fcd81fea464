package com.magnamedia.entity.PaymentSection;

import com.magnamedia.extra.LabelValueEnum;

import java.util.List;

public class PaymentSectionBox {

    public enum PaymentSectionBoxIcon implements LabelValueEnum {

        GREEN("accounting_payment_section_green_state_icon"),
        RED("accounting_payment_section_red_state_icon"),
        ORANGE("accounting_payment_section_orange_state_icon");

        private final String label;

        PaymentSectionBoxIcon(String label) { this.label = label; }

        @Override
        public String getLabel() { return label; }
    }

    public PaymentSectionBox(String borderColor, String cmsCode, String text, List<ActionButton> actionButtons, String icon) {
        this.borderColor = borderColor;
        this.cmsCode = cmsCode;
        this.text = text;
        this.actionButtons = actionButtons;
        this.icon = icon;
    }

    public PaymentSectionBox(
            String borderColor, String cmsCode, String text,
            List<ActionButton> actionButtons, String icon, String paragraphStartWithArrow) {
        this.borderColor = borderColor;
        this.cmsCode = cmsCode;
        this.text = text;
        this.actionButtons = actionButtons;
        this.icon = icon;
        this.paragraphStartWithArrow = paragraphStartWithArrow;
    }

    private String borderColor;

    private String cmsCode;

    private String text;

    private String title = "Monthly payment status";

    private String icon;

    private String paragraphStartWithArrow;

    private List<ActionButton> actionButtons;

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public String getCmsCode() {
        return cmsCode;
    }

    public void setCmsCode(String cmsCode) {
        this.cmsCode = cmsCode;
    }

    public List<ActionButton> getActionButtons() {
        return actionButtons;
    }

    public void setActionButtons(List<ActionButton> actionButtons) {
        this.actionButtons = actionButtons;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTitle() { return title; }

    public void setTitle(String title) { this.title = title; }

    public String getIcon() { return icon; }

    public void setIcon(String icon) { this.icon = icon; }

    public String getParagraphStartWithArrow() { return paragraphStartWithArrow; }

    public void setParagraphStartWithArrow(String paragraphStartWithArrow) { this.paragraphStartWithArrow = paragraphStartWithArrow; }
}
