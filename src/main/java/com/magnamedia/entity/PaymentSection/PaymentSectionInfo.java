package com.magnamedia.entity.PaymentSection;


import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class PaymentSectionInfo {

    public enum CardSectionState {
        ADD_NEW_CARD, EDIT_YOUR_CARD_DETAILS, WAITING_CARD_DETAILS, NONE
    }
    private List<PaymentSectionHeaderBox> headerBoxes;

    private String title;

    private PaymentSectionBox box;

    private String intro;

    private String introCmsCode;

    private Map<String, Object> bankDetails;

    private ReceivePaymentNotificationBox receivePaymentNotificationBox;

    private List<ActionButton> actionButtons;

    private String sectionKey;

    private CardSectionState cardSectionState = CardSectionState.NONE;

    private  Map<String, Object> cardSectionInfo;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public PaymentSectionBox getBox() {
        return box;
    }

    public void setBox(PaymentSectionBox box) {
        this.box = box;
    }

    public String getIntro() {
        return intro;
    }

    public void setIntro(String intro) {
        this.intro = intro;
    }

    public Map<String, Object> getBankDetails() {
        return bankDetails;
    }

    public void setBankDetails(Map<String, Object> bankDetails) {
        this.bankDetails = bankDetails;
    }

    public ReceivePaymentNotificationBox getReceivePaymentNotificationBox() { return receivePaymentNotificationBox; }

    public void setReceivePaymentNotificationBox(ReceivePaymentNotificationBox receivePaymentNotificationBox) {
        this.receivePaymentNotificationBox = receivePaymentNotificationBox;
    }

    public List<ActionButton> getActionButtons() {
        return actionButtons;
    }

    public void setActionButtons(List<ActionButton> actionButtons) {
        this.actionButtons = actionButtons;
    }

    public String getIntroCmsCode() {
        return introCmsCode;
    }

    public void setIntroCmsCode(String introCmsCode) {
        this.introCmsCode = introCmsCode;
    }

    public String getSectionKey() { return sectionKey; }

    public void setSectionKey(String sectionKey) { this.sectionKey = sectionKey; }

    public List<PaymentSectionHeaderBox> getHeaderBoxes() { return headerBoxes; }

    public void setHeaderBoxes(List<PaymentSectionHeaderBox> headerBoxes) { this.headerBoxes = headerBoxes; }

    public CardSectionState getCardSectionState() { return cardSectionState; }

    public void setCardSectionState(CardSectionState cardSectionState) { this.cardSectionState = cardSectionState; }

    public Map<String, Object> getCardSectionInfo() { return cardSectionInfo; }

    public void setCardSectionInfo(Map<String, Object> cardSectionInfo) { this.cardSectionInfo = cardSectionInfo; }
}

