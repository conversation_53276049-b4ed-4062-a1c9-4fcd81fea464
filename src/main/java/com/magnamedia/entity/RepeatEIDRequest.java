package com.magnamedia.entity;

import com.magnamedia.core.Setup;
import com.magnamedia.core.type.HousemaidStatus;
import com.magnamedia.core.workflow.FormField;
import com.magnamedia.module.type.HousemaidType;
import com.magnamedia.repository.ContractRepository;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Index;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.envers.NotAudited;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Aug 1, 2019
 * Jirra ACC-841
 */
@Entity
@Table(
        indexes = {
            @Index(columnList = "HOUSEMAID_ID", unique = false)
        })
public class RepeatEIDRequest extends VisaRequest<RepeatEIDRequest, RepeatEIDRequestNote, RepeatEIDRequestExpense> implements Serializable {

    @ManyToOne(fetch = FetchType.EAGER)
    private NewRequest newRequest;

    @Column
    @NotAudited
    private Boolean aramexPickedUpTheEidAndInsuranceCardFromTheOffice;

    @Column
    private String eidApplicationNumber;

    @Column
    private String newEidNumber;

    @Column
    @NotAudited
    private Boolean packageHasBeenHanded;

    @Transient
    private String clientAddress;

    @Column
    @NotAudited
    private Boolean eidAndInsuranceCardsPrepared;

    @Column
    @NotAudited
    private Boolean hasAramexAddressAndPhone = true;

    @Column
    @NotAudited
    private java.util.Date firstMessageSendingDate;

    @Column
    @NotAudited
    private java.util.Date yayaBotMessageDate;

    public RepeatEIDRequest() {
        super("Prepare EID Application");
    }

    @Override
    public List<FormField> getForm(String taskName) {
        return new ArrayList<>();
    }

    @Override
    public String getFinishedTaskName() {
        return "Repeat EID Request Completed";
    }

    public NewRequest getNewRequest() {
        return newRequest;
    }

    public void setNewRequest(NewRequest newRequest) {
        this.newRequest = newRequest;
    }

    public String getClientAddress() {
        if (clientAddress == null) {
            if (this.isforHousemaid()
                    && (this.getHousemaid().getHousemaidType().equals(HousemaidType.MAID_VISA)
                    || this.getHousemaid().getStatus() == HousemaidStatus.WITH_CLIENT)) {
                ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
                Contract contract = contractRepository.findFirstOneByHousemaidOrderByCreationDateDesc(this.getHousemaid());
                if (contract != null && contract.getClient() != null) {
                    clientAddress = contract.getClient().getFullAddress();
                }
            }
        }
        return clientAddress;
    }

    public void setClientAddress(String clientAddress) {
        this.clientAddress = clientAddress;
    }

    public Boolean getEidAndInsuranceCardsPrepared() {
        return eidAndInsuranceCardsPrepared;
    }

    public void setEidAndInsuranceCardsPrepared(Boolean eidAndInsuranceCardsPrepared) {
        this.eidAndInsuranceCardsPrepared = eidAndInsuranceCardsPrepared;
        if (this.getNewRequest() != null) {
            this.getNewRequest().setEidAndInsuranceCardsPrepared(eidAndInsuranceCardsPrepared);
        }
    }

    public Boolean getPackageHasBeenHanded() {
        return packageHasBeenHanded;
    }

    public void setPackageHasBeenHanded(Boolean packageHasBeenHanded) {
        this.packageHasBeenHanded = packageHasBeenHanded;
        if (this.getNewRequest() != null) {
            this.getNewRequest().setPackageHasBeenHanded(packageHasBeenHanded);
        }
    }

    public Boolean getAramexPickedUpTheEidAndInsuranceCardFromTheOffice() {
        return aramexPickedUpTheEidAndInsuranceCardFromTheOffice;
    }

    public void setAramexPickedUpTheEidAndInsuranceCardFromTheOffice(Boolean aramexPickedUpTheEidAndInsuranceCardFromTheOffice) {
        this.aramexPickedUpTheEidAndInsuranceCardFromTheOffice = aramexPickedUpTheEidAndInsuranceCardFromTheOffice;
        if (this.getNewRequest() != null) {
            this.getNewRequest().setAramexPickedUpTheEidAndInsuranceCardFromTheOffice(aramexPickedUpTheEidAndInsuranceCardFromTheOffice);
        }
    }

    public String getEidApplicationNumber() {
        return eidApplicationNumber;
    }

    public void setEidApplicationNumber(String eidApplicationNumber) {
        this.eidApplicationNumber = eidApplicationNumber;
        if (this.getNewRequest() != null) {
            this.getNewRequest().setEidApplicationNumber(eidApplicationNumber);
        }
    }

    public String getEntryVisaPermitNumber() {

        if (this.getNewRequest() != null) {
            return this.getNewRequest().getEntryVisaPermitNumber();
        }
        return "";
    }

    public String getMolCardNumber() {

        if (this.getNewRequest() != null) {
            return this.getNewRequest().getMolCardNumber();
        }
        return "";
    }

    public String getNewEidNumber() {
        return newEidNumber;
    }

    public void setNewEidNumber(String newEidNumber) {
        this.newEidNumber = newEidNumber;
        if (this.getNewRequest() != null) {
            this.getNewRequest().setNewEidNumber(newEidNumber);
        }
    }

    public Boolean getHasAramexAddressAndPhone() {
        return hasAramexAddressAndPhone;
    }

    public void setHasAramexAddressAndPhone(Boolean hasAramexAddressAndPhone) {
        this.hasAramexAddressAndPhone = hasAramexAddressAndPhone;
    }

    public Date getFirstMessageSendingDate() {
        return firstMessageSendingDate;
    }

    public void setFirstMessageSendingDate(Date firstMessageSendingDate) {
        this.firstMessageSendingDate = firstMessageSendingDate;
    }

    public Date getYayaBotMessageDate() {
        return yayaBotMessageDate;
    }

    public void setYayaBotMessageDate(Date yayaBotMessageDate) {
        this.yayaBotMessageDate = yayaBotMessageDate;
    }

    public String notifyMessage() {
        return "";
    }

    public String getPersonNameEnglish() {
        if (this.getNewRequest() != null) {
            return this.getNewRequest().getPersonNameEnglish();
        }
        return "";
    }

    public String getPersonNameArabic() {
        if (this.getNewRequest() != null) {
            return this.getNewRequest().getPersonNameArabic();
        }
        return "";
    }

    public String getCompanyCode() {
        if (this.getNewRequest() != null) {
            return this.getNewRequest().getCompanyCode();
        }
        return "";
    }

    public String getPassportId() {
        if (this.getNewRequest() != null) {
            return this.getNewRequest().getPassportId();
        }
        return "";
    }

    public java.sql.Date getBirthdate() {
        if (this.getNewRequest() != null) {
            return this.getNewRequest().getBirthdate();
        }
        return null;
    }

}