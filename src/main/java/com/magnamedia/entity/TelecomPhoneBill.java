package com.magnamedia.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.Setup;
import com.magnamedia.core.annotation.Label;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ManyToOne;
import javax.persistence.Transient;

import com.magnamedia.helper.DateUtil;
import org.aspectj.lang.annotation.AfterReturning;

/**
 *
 * <AUTHOR> <<EMAIL>>
 * Created on Mar 1, 2018
 */
@Entity
public class TelecomPhoneBill extends BaseEntity {

    @Label
    private String name;
    
    @ManyToOne
    @JsonSerialize(using = CustomIdLabelSerializer.class)
    private TelecomPhone phone;
    
    @Column
    private Integer amount;
    
    @Column
    private Date billDate;
    
    @Transient
    private Date attachmentDate;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TelecomPhone getPhone() {
        return phone;
    }

    public void setPhone(TelecomPhone phone) {
        this.phone = phone;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Date getBillDate() {
        return billDate;
    }
    
    public String getFormattedBillDate() {
        return billDate!=null ? DateUtil.formatFullDate(billDate) : "";
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Date getAttachmentDate() {
        AttachementRepository attachementRepository =
                Setup.getRepository(AttachementRepository.class);
        List<Attachment> attachments =
                attachementRepository.
                        findByOwnerIdAndOwnerType(getId(), "TelecomPhoneBill");
        this.attachmentDate = attachments.size()>0 ?
                attachments.get(0).getCreationDate() : this.attachmentDate;
        return attachmentDate;
    }

    public void setAttachmentDate(Date attachmentDate) {
        this.attachmentDate = attachmentDate;
    }
    
    @AfterReturning
    public void setAttachmentDate(){
        AttachementRepository attachementRepository =
                Setup.getRepository(AttachementRepository.class);
        List<Attachment> attachments =
                attachementRepository.
                        findByOwnerIdAndOwnerType(getId(), "TelecomPhoneBill");
        this.attachmentDate = attachments.size()>0 ?
                attachments.get(0).getCreationDate() : null;
    }
}
