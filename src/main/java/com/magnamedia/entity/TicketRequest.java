package com.magnamedia.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.entity.serializer.CustomIdLabelSerializer;
import java.sql.Date;
import java.sql.Time;
import java.util.List;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Transient;

/**
 * <AUTHOR> Created on 2017-07-19
 *
 */
@Entity
public class TicketRequest extends BaseEntity {

	public enum TicketRequestType {
		HOUSEMAID_TO_EXIT,
		HOUSEMAID_TO_DUBAI,
		HOUSEMAID_VACATIONS,
		HOUSEMAID_TERMINATION,
		HOUSEMAID_PREWORK_VACATION,
		OFFICE_STAFF
	}

	@Column
	@Enumerated(EnumType.STRING)
	private TicketRequestType ticketType;

	@Column(length = 1000)
	private String notes;

	@Column(length = 1000)
	private String specialRequests;

	private Date preferredDate;

	private Time preferredTime;

	@Column(length = 1000)
	private String phoneNumber;

	@Column
	private Date passportExpiryDate;

	@Column
	private Boolean isUrgent;

	@Column
	private Boolean isChangeDateRequest;

	@Column
	private Boolean isProcessed;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = CustomIdLabelSerializer.class)
	private Housemaid housemaid;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = CustomIdLabelSerializer.class)
	private OfficeStaff officeStaff;

	@ManyToOne(fetch = FetchType.LAZY)
	@JsonSerialize(using = CustomIdLabelSerializer.class)
	private PicklistItem airport;

	@OneToMany(mappedBy = "ticketrequest",
			   fetch = FetchType.LAZY,
			   cascade = CascadeType.REMOVE)
	private List<TicketComment> ticketComments;

	@OneToMany(mappedBy = "request",
			   fetch = FetchType.LAZY,
			   cascade = CascadeType.REMOVE)
	@JsonIgnore
	private List<Ticket> tickets;

	//////////// Search Attributes /////////////
	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private String searchName;

	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private String searchTicketType;

	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private Date searchFromDate;

	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private Date searchToDate;

	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private Date searchFromPreferredDate;

	@Transient
	@JsonProperty(access = Access.WRITE_ONLY)
	private Date searchToPreferredDate;

	////////// Transient Attributes /////////
	@Transient
	@JsonIgnore
	private boolean forceDelete = false; // to delete the request has related tickets

	public TicketRequestType getTicketType() {
		return ticketType;
	}

	public void setTicketType(TicketRequestType ticketType) {
		this.ticketType = ticketType;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getSpecialRequests() {
		return specialRequests;
	}

	public void setSpecialRequests(String specialRequests) {
		this.specialRequests = specialRequests;
	}

	public Date getPreferredDate() {
		return preferredDate;
	}

	public void setPreferredDate(Date preferredDate) {
		this.preferredDate = preferredDate;
	}

	public Time getPreferredTime() {
		return preferredTime;
	}

	public void setPreferredTime(Time preferredTime) {
		this.preferredTime = preferredTime;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public Date getPassportExpiryDate() {
		return passportExpiryDate;
	}

	public void setPassportExpiryDate(Date passportExpiryDate) {
		this.passportExpiryDate = passportExpiryDate;
	}

	public Boolean getIsChangeDateRequest() {
		return isChangeDateRequest;
	}

	public void setIsChangeDateRequest(Boolean isChangeDateRequest) {
		this.isChangeDateRequest = isChangeDateRequest;
	}

	public Boolean getIsProcessed() {
		return isProcessed;
	}

	public void setIsProcessed(Boolean isProcessed) {
		this.isProcessed = isProcessed;
	}

	public Boolean getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(Boolean isUrgent) {
		this.isUrgent = isUrgent;
	}

	public Housemaid getHousemaid() {
		return housemaid;
	}

	public void setHousemaid(Housemaid housemaid) {
		this.housemaid = housemaid;
	}

	public OfficeStaff getOfficeStaff() {
		return officeStaff;
	}

	public void setOfficeStaff(OfficeStaff officeStaff) {
		this.officeStaff = officeStaff;
	}

	public PicklistItem getAirport() {
		return airport;
	}

	public void setAirport(PicklistItem airport) {
		this.airport = airport;
	}

	public List<TicketComment> getTicketComments() {
		return ticketComments;
	}

	public void setTicketComments(List<TicketComment> ticketComments) {
		this.ticketComments = ticketComments;
	}

	public String getSearchName() {
		return searchName;
	}

	public void setSearchName(String searchName) {
		this.searchName = searchName;
	}

	public String getSearchTicketType() {
		return searchTicketType;
	}

	public void setSearchTicketType(String searchTicketType) {
		this.searchTicketType = searchTicketType;
	}

	public Date getSearchFromDate() {
		return searchFromDate;
	}

	public void setSearchFromDate(Date searchFromDate) {
		this.searchFromDate = searchFromDate;
	}

	public Date getSearchToDate() {
		return searchToDate;
	}

	public void setSearchToDate(Date searchToDate) {
		this.searchToDate = searchToDate;
	}

	public Date getSearchFromPreferredDate() {
		return searchFromPreferredDate;
	}

	public void setSearchFromPreferredDate(Date searchFromPreferredDate) {
		this.searchFromPreferredDate = searchFromPreferredDate;
	}

	public Date getSearchToPreferredDate() {
		return searchToPreferredDate;
	}

	public void setSearchToPreferredDate(Date searchToPreferredDate) {
		this.searchToPreferredDate = searchToPreferredDate;
	}

	public void setForceDelete(boolean forceDelete) {
		this.forceDelete = forceDelete;
	}

	public List<Ticket> getTickets() {
		return tickets;
	}

	public boolean isForceDelete() {
		return forceDelete;
	}

}
