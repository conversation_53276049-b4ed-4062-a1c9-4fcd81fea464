

package com.magnamedia.entity;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.mastersearch.SearchableField;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;

/**
 *
 * <AUTHOR> <<EMAIL>> 
 * Created at Mar 15, 2018
 */

@Entity
public class TotalLoansCapture extends BaseEntity {
    
    @Column
    private double totalLoansAmount;
    @Column
    private Date dateOfCapture;

    public double getTotalLoansAmount() {
        return totalLoansAmount;
    }

    public void setTotalLoansAmount(double totalLoansAmount) {
        this.totalLoansAmount = totalLoansAmount;
    }


    public Date getDateOfCapture() {
        return dateOfCapture;
    }

    public void setDateOfCapture(Date dateOfCapture) {
        this.dateOfCapture = dateOfCapture;
    }
    

}
