package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Bucket;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Oct 12, 2020
 */
public class BucketIdLabelCodeSerializer extends JsonSerializer<Bucket> {

	@Override
	public void serialize(Bucket value, JsonGenerator gen, SerializerProvider serializers)
		throws IOException, JsonProcessingException {
		if (value == null) {
			gen.writeNull();
			return;
		}
		gen.writeStartObject();
		gen.writeNumberField("id", value.getId());
		gen.writeStringField("label", value.getName());
		gen.writeString<PERSON>ield("code", value.getCode());
		gen.writeEndObject();
	}

}
