package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Transaction;

import java.io.IOException;

/**
 * Created by hp on 4/13/2021.
 */
public class ExpensePaymentTransactionSerializer extends JsonSerializer<Transaction> {

    @Override
    public void serialize(Transaction value, JsonGenerator gen, SerializerProvider sp) throws IOException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();

        gen.writeObjectField("id", value.getId() != null ? value.getId() : null);
        gen.writeNumberField("amount", value.getAmount() != null ? value.getAmount() : 0);
        if (value.getFromBucket() != null) {
            gen.writeObjectFieldStart("fromBucket");
            gen.writeNumberField("id", value.getFromBucket().getId());
            gen.writeStringField("name", value.getFromBucket().getName());
            gen.writeStringField("code", value.getFromBucket().getCode());
            gen.writeStringField("label", value.getFromBucket().getLabel());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("fromBucket", "");
        }

        if (value.getToBucket() != null) {
            gen.writeObjectFieldStart("toBucket");
            gen.writeNumberField("id", value.getToBucket().getId());
            gen.writeStringField("name", value.getToBucket().getName());
            gen.writeStringField("code", value.getToBucket().getCode());
            gen.writeStringField("label", value.getToBucket().getLabel());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("toBucket", "");
        }

        gen.writeStringField("description", value.getDescription() != null ? value.getDescription().toString() : "");

        if (value.getExpense() != null) {
            gen.writeObjectFieldStart("expense");
            gen.writeNumberField("id", value.getExpense().getId());
            gen.writeStringField("name", value.getExpense().getName());
            gen.writeStringField("code", value.getExpense().getCode());
            gen.writeStringField("label", value.getExpense().getLabel());
            gen.writeStringField("caption", value.getExpense().getCaption());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("expense", "");
        }

        if (value.getRevenue() != null) {
            gen.writeObjectFieldStart("revenue");
            gen.writeNumberField("id", value.getRevenue().getId());
            gen.writeStringField("name", value.getRevenue().getName());
            gen.writeStringField("code", value.getRevenue().getCode());
            gen.writeStringField("label", value.getRevenue().getLabel());
            gen.writeEndObject();
        } else {
            gen.writeObjectField("revenue", "");
        }

        gen.writeStringField("date", value.getDate() != null ? value.getDate().toString() : "");
        gen.writeEndObject();
    }

}
