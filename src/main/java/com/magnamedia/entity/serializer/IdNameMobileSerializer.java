package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Client;
import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Dec 18, 2018
 */
public class IdNameMobileSerializer extends JsonSerializer<Client>{

    @Override
  public void serialize(Client value,
            JsonGenerator gen,
            SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("name",
                value.getName());
        if(value.getMobileNumber()!= null){
            gen.writeStringField("mobileNumber", value.getMobileNumber());
        }else{
            gen.writeStringField("mobileNumber", "");
        }
        
        gen.writeEndObject();
    }
}
