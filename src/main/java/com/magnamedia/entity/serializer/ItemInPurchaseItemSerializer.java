package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.entity.Item;

import java.io.IOException;

/**
 * <PERSON> (Feb 15, 2021)
 */
public class ItemInPurchaseItemSerializer extends JsonSerializer<Item> {
    @Override
    public void serialize(Item value,
                          JsonGenerator gen,
                          SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id", value.getId());
        gen.writeStringField("name", value.getName());
        gen.writeStringField("measure", value.getUnitOfMeasure() != null ? value.getUnitOfMeasure().getShortName() : null);
        gen.writeStringField("creationDate", value.getCreationDate() != null ? value.getCreationDate().toString() : null);
        gen.writeEndObject();
    }

}
