package com.magnamedia.entity.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.magnamedia.core.entity.PicklistItem;

import java.io.IOException;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Jun 27, 2018
 */

public class PickListItemSerializer extends JsonSerializer<PicklistItem> {

    @Override
    public void serialize(PicklistItem value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException, JsonProcessingException {
        if (value == null) {
            gen.writeNull();
            return;
        }
        gen.writeStartObject();
        gen.writeNumberField("id",
                value.getId());
        gen.writeStringField("code",
                value.getCode());
        gen.writeEndObject();
    }

}
