package com.magnamedia.report;

import com.magnamedia.entity.AdhocVariableNode;
import com.magnamedia.entity.BaseReportCompany;
import com.magnamedia.entity.BasePLNode;
import com.magnamedia.entity.interfaces.BasePLVariableNode;
import com.magnamedia.helper.DateUtil;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <<EMAIL>>
 *         Created on Aug 20, 2020
 *         Jirra ACC-644
 */

public class ADHOC_Report extends BaseReport {
    enum Format {HTML, EXCEL, PDF}

    //Jirra ACC-824
    private DecimalFormat myFormatter = new DecimalFormat("###,###");

    private final BaseReportCompany pLCompany;
    private final Date fromDate;
    private final Date toDate;
    private final String[] headers;
    //Jirra ACC-385
    private boolean colored = true;
    private Integer maxLevel = 2;
    private String baseUrl;
    private PNL_Report.Format format;
    private boolean showFormula;

    public ADHOC_Report(
            BaseReportCompany pLCompany, Date fromDate, Date toDate, boolean colored, Integer maxLevel, String baseUrl, String format, boolean showFormula) {
        this.pLCompany = pLCompany;
        this.fromDate = fromDate;
        this.toDate = toDate;
        if (showFormula)
            this.headers = new String[]{"Name", "Amount (AED)", "Ratio", "Formula"};
        else
            this.headers = new String[]{"Name", "Amount (AED)", "Ratio"};

        this.colored = colored;
        this.maxLevel = maxLevel;
        this.baseUrl = baseUrl;
        this.format = PNL_Report.Format.valueOf(format);
        this.showFormula = showFormula;
    }

    private Cell[] getNodeNameRow(BasePLNode pLNode) {
        Cell[] res = new Cell[1];
        res[0] = new Cell(pLNode.getName().replace("&", "&amp;"))
                .withBold(true)
                .withColumnSpan(headers.length)
                .withAlign(Align.Left);
        if (colored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty()))
            for (Cell c : res)
                c.withStyle("background-color:" + pLNode.getReportColor() + " !important");
        return res;
    }

    private Cell[] getVariableRow(BasePLNode pLVariableNode) {

        Cell[] res = new Cell[headers.length];
        if (format == PNL_Report.Format.HTML) {
//            Jirra ACC-2369
            res[0] = new Cell("<a href='javascript:void(0);' data-toggle='modal' "
                    + "data-target='#showtransactionsbehindrow_modal' "
                    + "class='showdetails' data-id='" + pLVariableNode.getId() + "' >"
                    + pLVariableNode.getName().replace("&", "&amp;")
                    + "</a>"
            ).withAlign(Align.Left);
        } else
            res[0] = new Cell(pLVariableNode.getName().replace("&", "&amp;")).withAlign(Align.Left);
        //Jirra ACC-755
        res[1] = new Cell(myFormatter.format(Math.round(pLVariableNode.getValue())));
        res[2] = new Cell(("%" + String.format("%,.1f", (double) pLVariableNode.getRatio())).replace("&", "&amp;"));

        if (showFormula)
            res[3] = new Cell(((BasePLVariableNode) pLVariableNode).getFormula().replace("&", "&amp;"));
        if ((colored) && (pLVariableNode.getReportColor() != null) && (!pLVariableNode.getReportColor().isEmpty())) {
            for (Cell c : res)
                c.withStyle("background-color:" + pLVariableNode.getReportColor() + " !important");
        }
        return res;
    }

    private Cell[] getNodeTotalRow(BasePLNode pLNode) {
        Cell[] res = new Cell[headers.length];
        res[0] = new Cell("Total " + pLNode.getName().replace("&", "&amp;")).withBold(true)
                .withAlign(Align.Left);
        //Jirra ACC-755
        res[1] = new Cell(myFormatter.format(Math.round(pLNode.getValue())));
        //res[1] = new Cell(String.format("%,.2f", pLNode.getValue()));
        //Jirra ACC-1389

        //ACC-638
        //Jirra ACC-644
        res[2] = new Cell(("%" + String.format("%,.1f", (double) (pLNode.getParent() == null ? pLNode.getRatio() / 10 : pLNode.getRatio()))).replace("&", "&amp;"));

        if (showFormula)
            res[3] = new Cell("");
        if (colored && (pLNode.getReportColor() != null) && (!pLNode.getReportColor().isEmpty()))
            for (Cell c : res)
                c.withStyle("background-color:" + pLNode.getReportColor() + " !important");
        return res;
    }

    @Override
    public void build() {
        List<Column> columnList = new ArrayList<>();
        addSection(
                new Text(new TextTitle(pLCompany.getReportTitle())
                        .withAlign(Align.Left)
                        .withBold(true)
                        .withFontSize(Size.Large),
                        ""));

        addSection(
                new Text("",
                        "From Date: " + DateUtil.formatFullDate(fromDate))
                        .withAlign(Align.Left).withBold(true));

        addSection(
                new Text("",
                        "  To Date: " + DateUtil.formatFullDate(toDate))
                        .withAlign(Align.Left).withBold(true));

        for (String header : headers) {
            columnList.add(new Column(new ColumnTitle(header).withAlign(Align.Center).withBold(true)));
        }
        Column[] columns = new Column[headers.length];
        columnList.toArray(columns);

        for (BasePLNode pLHeadNode : pLCompany.getSortedChildren()) {
            if (colored && (pLHeadNode.getReportColor() != null) && (!pLHeadNode.getReportColor().isEmpty()))
                for (Column c : columns)
                    c.withStyle("background-color:" + pLHeadNode.getReportColor() + " !important");
            else
                for (Column c : columns)
                    c.withStyle("");
            buildHeadlevelTable(columns, pLHeadNode);
        }
    }

    public void buildHeadlevelTable(Column[] columns, BasePLNode pLHeadNode) {
        TableTitle tt = new TableTitle(pLHeadNode.getName()).withAlign(Align.Left);
        if (this.colored && (pLHeadNode.getReportColor() != null) && (!pLHeadNode.getReportColor().isEmpty()))
            tt.withStyle("background-color:" + pLHeadNode.getReportColor() + " !important");
        else
            tt.withBackColor(Color.Grey);
        Table data = new Table(tt, columns);
        for (BasePLNode pLNode : (List<BasePLNode>) pLHeadNode.getSortedChildren()) {
            buildNodeBlock(columns, pLNode, data, null);
        }
        data.addRow(getNodeTotalRow(pLHeadNode));

        addSection(data);
    }

    public void buildNodeBlock(Column[] columns, BasePLNode pLNode, Table data, Integer level) {
        if (level != null && level == maxLevel)
            data.addRow(getVariableRow(pLNode));
        else if (pLNode.getType().equals(AdhocVariableNode.class.getSimpleName()))
            data.addRow(getVariableRow(pLNode));
        else {
            data.addRow(getNodeNameRow(pLNode));
            for (BasePLNode node : (List<BasePLNode>) pLNode.getSortedChildren()) {
                if (level != null) {
                    Integer level2 = level + 1;
                    buildNodeBlock(columns, node, data, level2);
                } else
                    buildNodeBlock(columns, node, data, null);
            }
            data.addRow(getNodeTotalRow(pLNode));
        }

    }
}
