package com.magnamedia.service;


import com.magnamedia.core.Setup;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.Replacement;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.repository.ComplaintRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.repository.PaymentRepository;
import com.magnamedia.repository.ReplacementRepository;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.module.AccountingModule.PARAMETER_ACC5044_CONTRACT_CREATION_DATE;
import static com.magnamedia.module.AccountingModule.PARAMETER_ACC5044_MONTHLY_PAYMENT_AMOUNT;


// Jira ACC-5044
@Service
public class ComplaintService {

    protected final static Logger logger = Logger.getLogger(ComplaintService.class.getName());

    @Autowired
    private ComplaintRepository complaintRepository;

    public void complyWithMOHORE(
            ContractPaymentTerm cpt,
            Replacement replacement) {

        try {
            Contract contract = cpt.getContract();
            if (!contract.isMaidCc() || !contract.getStatus().equals(ContractStatus.ACTIVE)) return;

            if (replacement.getNewHousemaid() == null || replacement.getNewHousemaid().getId() == null) return;

            LocalDate creationDate = new LocalDate(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_ACC5044_CONTRACT_CREATION_DATE));
            if (new LocalDate(contract.getCreationDate()).isAfter(creationDate)) {
                logger.log(Level.INFO, "Contract date: {0}; para value: {1}",
                        new Object[]{contract.getCreationDate(), creationDate});
                return;
            }

            if(complaintRepository.existsByComplaintAddedBy5044(contract)) {
                logger.log(Level.INFO, "complaint already added contract id: {0}", contract.getId());
                return;
            }

            Double ddAmount = Setup.getRepository(DirectDebitRepository.class)
                    .findAmountByDdbConfirmed(cpt);
            if (ddAmount == null) {
                logger.log(Level.INFO, "There is no dd confirmed contract id: {0}", contract.getId());
                List<Double> d = Setup.getRepository(PaymentRepository.class)
                        .findAmountByLastMonthlyPaymentReceived(contract);

                if (!d.isEmpty()) {
                    ddAmount = d.get(0);
                } else {
                    logger.log(Level.INFO, "There is no payment received contract id: {0}", contract.getId());
                    ddAmount = Setup.getApplicationContext()
                            .getBean(CalculateDiscountsWithVatService.class)
                            .getMonthlyPayment(cpt, new Date());
                }
            }

            Double AllowedAmount = Double.valueOf(Setup.getParameter(Setup.getCurrentModule(), PARAMETER_ACC5044_MONTHLY_PAYMENT_AMOUNT));
            if (ddAmount < AllowedAmount) {
                logger.log(Level.INFO, "ddAmount: {0}; para value: {1}",
                        new Object[]{ddAmount, AllowedAmount});
                return;
            }

            if (Setup.getRepository(ReplacementRepository.class)
                    .existsByContractAndIdNotAndNewHousemaidIsNotNullAndCreationDateGreaterThan(
                            contract, replacement.getId(), creationDate.dayOfMonth().withMaximumValue().toDate())) {
                logger.log(Level.INFO, "there is replacement added before contract id: {0}", contract.getId());
                return;
            }

            String description = "Greetings from Maids.cc. Government regulations require us to change your monthly agreement to a weekly agreement*. " +
                    "The only change will be that you'll start paying AED 95 less " +
                    "(Total of AED @new_amount@ instead of AED @old_amount@) on your Monthly Bank Payment Form. " +
                    "(For the record, you'll be paying AED @weekly_amount@/week + AED 113/week to cover your maid’s " +
                    "government-mandated benefits + VAT of AED @vat_amount@/week.) \n " +
                    "*If you still want to keep your agreement as a monthly contract, " +
                    "please contact us to select an African or Ethiopian maid for AED 3,500/month. \n" +
                    "To confirm your reduced payment of AED @reduced_amount@ on your Monthly Bank Payment Form, please reply to this message with \"Confirm\".";

            Double newAmount = ddAmount - 95;
            Double weeklyAmount = newAmount / 4;
            Double vatAmountPerWeek = weeklyAmount * 0.05;
            description = description.replace("@new_amount@", String.valueOf(newAmount.intValue()))
                    .replace("@old_amount@", String.valueOf(ddAmount.intValue()))
                    .replace("@weekly_amount@", weeklyAmount.toString())
                    .replace("@vat_amount@", vatAmountPerWeek.toString())
                    .replace("@reduced_amount@", String.valueOf(newAmount.intValue()));


            // create a complaint
            Map<String, Object> data = new HashMap<>();
            data.put("clientId", contract.getClient().getId());
            data.put("contractId", contract.getId());
            data.put("initialDescription", description);
            data.put("assigneeTeam", "Validators_1624260361040");
            data.put("primaryType", "Contract-amendments");
            data.put("housemaidId", replacement.getNewHousemaid().getId());

            Setup.getApplicationContext().getBean(InterModuleConnector.class)
                    .postJsonAsync("complaints/complaint/add", data);

        } catch (Exception ex) {
            logger.severe("error in complying with MOHRE");
            ex.printStackTrace();
        }
    }
}
