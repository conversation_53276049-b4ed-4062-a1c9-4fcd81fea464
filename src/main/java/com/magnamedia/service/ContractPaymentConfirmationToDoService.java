package com.magnamedia.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.epayment.EPaymentTransaction;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.*;
import com.magnamedia.core.helper.epayment.ETransactionStatus;
import com.magnamedia.core.helper.epayment.EpaymentTransactionType;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.repository.ParameterRepository;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.core.repository.PushNotificationRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.NotificationLocation;
import com.magnamedia.entity.*;
import com.magnamedia.entity.ContractPaymentConfirmationToDo.Source;
import com.magnamedia.entity.ccapp.CcAppTracking;
import com.magnamedia.entity.dto.PaymentCollectedAfterTerminationCsv;
import com.magnamedia.entity.dto.ExistingPaymentFromGPTDto;
import com.magnamedia.entity.dto.ContractPaymentConfirmationToDoFromGPTDto;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.*;
import com.magnamedia.extra.DTOs.DirectDebitDTO;
import com.magnamedia.extra.EmailHelper;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.extra.annotations.UsedBy;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class ContractPaymentConfirmationToDoService {
    private static final Logger logger = Logger.getLogger(ContractPaymentConfirmationToDoService.class.getName());

    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private AfterCashFlowService afterCashFlowService;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private ClientPayingViaCreditCardService clientPayingViaCreditCardService;
    @Autowired
    private InterModuleConnector moduleConnector;
    @Autowired
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    @Autowired
    private Shortener shortener;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private OneMonthAgreementFlowService oneMonthAgreementFlowService;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private AccountingEPaymentService accountingEPaymentService;
    @Autowired
    private CreditCardOfferService creditCardOfferService;
    @Autowired
    private UnpaidOnlineCreditCardPaymentService unpaidOnlineCreditCardPaymentService;
    @Autowired
    private BouncingFlowService bouncingFlowService;
    @Autowired
    private ContractPaymentWrapperRepository contractPaymentWrapperRepository;
    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private PicklistItemRepository picklistItemRepository;
    @Autowired
    private ParameterRepository parameterRepository;

    public void paidViaPayTabs(
            Long todoID,
            Boolean paidSuccess,
            String respMessage) throws Exception {

        paidViaPayTabs(todoID, paidSuccess, respMessage, "");
    }

    public void paidViaPayTabs(
            Long todoID,
            Boolean paidSuccess,
            String respMessage,
            String flagText) throws Exception {

        ContractPaymentConfirmationToDo t = contractPaymentConfirmationToDoRepository.findOne(todoID);

        // ACC-6513
        SelectQuery<EPaymentTransaction> q = new SelectQuery<>(EPaymentTransaction.class);
        q.filterBy("localReference", "=", accountingEPaymentService.getToDoIdentifier(t));
        if (paidSuccess) {
            q.filterBy("transactionStatus", "=", ETransactionStatus.SUCCESS);
        }
        q.sortBy("creationDate", true, true);
        q.setLimit(1);

        List<EPaymentTransaction> l = q.execute();
        if (!l.isEmpty()) {
            logger.info("transactionType: " + l.get(0).getTransactionType());
            switch (l.get(0).getProvider()) {
                case PAYTABS:
                    t.setPayTabsResponseMessage(l.get(0).getStatusDetails());
                    break;
                case CHECKOUT:
                    t.setCheckoutResponseMessage(l.get(0).getStatusDetails());
                    break;
            }

            if (paidSuccess) {
                t.setAuthorizationCode(l.get(0).getAuthorizationCode());
                t.setTransferReference(l.get(0).getTransReference());
            }

            // ACC-6840
            if (t.getContractPaymentTerm().isActive() && l.get(0).getSourceId() != null && Arrays.asList(
                    EpaymentTransactionType.RECURRING_CLIENT_INITIATED,
                            EpaymentTransactionType.AUTHORIZATION)
                    .contains(l.get(0).getTransactionType())) {
                checkIfSourceIdChanged(t.getContractPaymentTerm(), l.get(0), t);
            }
        }

        if (t.getSource().equals(Source.AUTHORIZATION)) {
            logger.info("todo id : " + t.getId() + "; source: AUTHORIZATION" );
            t.setDisabled(true);
            contractPaymentConfirmationToDoRepository.save(t);
            return;
        }

        if (paidSuccess) {
            switch (t.getSource()) {
                case AFTER_CASH_FLOW:
                    afterCashFlowService.createPaymentAfterPaidSuccess(t);
                    break;
                case CLIENT_PAYING_VIA_Credit_Card:
                    clientPayingViaCreditCardService.createPaymentAfterPaidSuccess(t);
                    break;
                case ONE_MONTH_AGREEMENT:
                    oneMonthAgreementFlowService.createPaymentAfterPaidSuccess(t);
                    break;
                case ERP:
                case PAYMENT_REMINDER:
                    resolveErpTodoPayment(t);
                    break;
                case BOUNCED_PAYMENT_FLOW:
                    Payment bouncedPayment = paymentRepository.findOne(t.getContractPaymentList().get(0).getReplacedBouncedPaymentId());
                    addReplacementPayment(bouncedPayment, t.getContractPaymentList().get(0), true);
                    break;
                case FAQ:
                case SWITCH_NATIONALITY:
                case INCOMPLETE_FLOW_DATA_ENTRY_REJECTION:
                case INCOMPLETE_FLOW_MISSING_BANK_INFO:
                case DD_REJECTED:
                    for (ContractPaymentWrapper w : t.getContractPaymentList()) {
                        createPayment(w, t.getAttachments(), PaymentStatus.RECEIVED, true);
                    }
                    break;
                case VISA_OVERSTAY_FEE:
                    createPayment(t.getContractPaymentList().get(0), t.getAttachments(), PaymentStatus.RECEIVED, true);
                    callAfterPayFinsBGT(t);
                    break;
                case DD_FLOWS_DYNAMIC_WEB_PAGE:
                    Setup.getApplicationContext()
                            .getBean(DirectDebitRejectionFlowService.class)
                            .handlePaymentAfterPaidSuccess(t);
                    break;
            }

            if (t.isCreditCardOffer()) {
                creditCardOfferService.paymentReceived(t);
            }

            if (t.isCreditCardOffer()) {
                creditCardOfferService.paymentReceived(t);
            }

            t.setPayTabsResponseMessage(respMessage);
            t.setShowOnERP(true);
            t.setCardPaymentReceivedDate(new Date());
            contractPaymentConfirmationToDoRepository.save(t);

            disableOnPaytabPaymentReceived(t.getContractPaymentTerm().getContract());

            //ACC-3968
            for (ContractPaymentWrapper contractPaymentWrapper : t.getContractPaymentList()) {
                if (contractPaymentWrapper.isReplacementOfBouncedPayment()) {
                    logger.info("Replacement Of Bounced Payment");
                    paymentService.pauseBouncingFlow(contractPaymentWrapper.getReplacedBouncedPaymentId());
                }
            }

            // ACC-5156 ACC-5553
            Setup.getApplicationContext().getBean(FlowProcessorService.class)
                    .retractContractTermination(t.getContractPaymentTerm(), t);
        } else {
            t.setPayTabsResponseMessage(respMessage);
            contractPaymentConfirmationToDoRepository.save(t);
        }
    }

    public  Map<String, Object> createFromGPTAPI(ContractPaymentConfirmationToDoFromGPTDto dto) {
        Map<String, Object> result = new HashMap<>();
        String payTabsPaymentLink = "",reasonOfFailure = ""; boolean requestSuccessful = false;

        try {
            checkDTOValidation(dto);
            ContractPaymentConfirmationToDo toDo = prepareTodoForGPT(dto);
            validateCreation(toDo);
            Map<String, Object> m = validateCreateToDoFromErp(toDo);
            if(!m.get("valid").equals(true)) {
                if (m.containsKey("url"))
                    payTabsPaymentLink = (String) m.get("url");
                else
                    throw new BusinessException((String) m.getOrDefault("message", "validation failed"));
            }

            if (payTabsPaymentLink.isEmpty()) {
                toDo = createToDoERPSource(toDo);

                if (toDo == null) {
                    throw new BusinessException("Unknown error");
                }

                payTabsPaymentLink = getPayingViaCreditCardLink(toDo);
            }
            requestSuccessful = true;
        } catch (Exception e) {
            e.printStackTrace();
            payTabsPaymentLink = "";
            reasonOfFailure = e.getMessage();
        }

        result.put("RequestSuccessful", requestSuccessful && !payTabsPaymentLink.isEmpty());
        result.put("Link", payTabsPaymentLink);
        result.put("ReasonOfFailure", reasonOfFailure);
        return result;
    }

    private void checkDTOValidation(ContractPaymentConfirmationToDoFromGPTDto dto) {
        if (dto.getContractId() == null) throw new BusinessException("Contract ID is required");
        if (dto.getPaymentMethod() == null) throw new BusinessException("Payment Method is required");
        if (!dto.getPaymentMethod().equals(PaymentMethod.CARD.toString())) throw new BusinessException("Payment Method must be CARD");
        if (dto.getNewPaymentDate() == null || dto.getNewPaymentDate().isEmpty()) throw new BusinessException("New Payment Date is required");
        if (dto.getPaymentType() == null || dto.getPaymentType().isEmpty()) throw new BusinessException("Payment Type is required");

        PicklistItem paymentType = PicklistHelper.getItemNoException("TypeOfPayment", dto.getPaymentType());
        if (paymentType == null) throw new BusinessException("Invalid Payment Type: " + dto.getPaymentType());

        if (dto.hasBothDiscountAndAddition()) {
            throw new BusinessException("Both discount and addition cannot be provided together. Please provide either discount or addition.");
        }

        try {
            dto.setParsedDate(DateTimeFormat.forPattern("yyyy-MM-dd").parseDateTime(dto.getNewPaymentDate()).toDate());
        } catch (IllegalArgumentException e) {
            throw new BusinessException("Date format must be yyyy-MM-dd");
        }
    }

    private ContractPaymentConfirmationToDo prepareTodoForGPT(ContractPaymentConfirmationToDoFromGPTDto dto) {
        Contract contract = contractRepository.findOne(dto.getContractId());
        if (contract == null) throw new BusinessException("Contract Not Found");

        if (contract.isMaidVisa() && dto.isNewPaymentProrated()) {
            throw new BusinessException("Pro-rated payments are not allowed for MV contracts");
        }

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();

        logger.info("cpt id : " + cpt.getId());
        PicklistItem paymentType = PicklistHelper.getItem("TypeOfPayment", dto.getPaymentType());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();

        todo.setContractPaymentTerm(cpt);
        todo.setPaymentMethod(PaymentMethod.valueOf(dto.getPaymentMethod()));
        todo.setPayingOnline(dto.isPayingOnline());
        if (paymentType.hasTag("required_in_add_payment_for_approval_page")) {
            todo.setRequired(true);
        } else {
            todo.setRequired(dto.isRequired());
        }
        if(dto.getDescription() != null) {
            todo.setDescription(dto.getDescription());
        } else {
            todo.setDescription("Added from GPT");
            todo.setPurpose(ContractPaymentConfirmationToDo.Purpose.NEW_GPT);
        }
        todo.setPaymentType(paymentType);

        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setProrated(dto.isNewPaymentProrated());
        wrapper.setPaymentDate(dto.getParsedDate());
        wrapper.setPaymentType(paymentType);
        wrapper.setContractPaymentConfirmationToDo(todo);

        Double finalAmount;
        if (dto.hasAmount()) {
            finalAmount = dto.getAmount();
            if (dto.hasDiscount()) {
                wrapper.setAdditionalDiscountAmount(dto.getDiscountAmount());
                wrapper.setAffectedByAdditionalDiscount(true);
                finalAmount = (Double) calculateDiscountsWithVatService.getPaymentAmountAndAdditionalDiscount(dto.getDiscountAmount(), finalAmount).get("amount");
            } else if (dto.hasAddition()) {
                wrapper.setAdditionAmount(dto.getAdditionAmount());
                finalAmount = calculateDiscountsWithVatService.getPaymentAmountWithAddOn(dto.getAdditionAmount(), dto.getAmount());
            }
            finalAmount = Math.floor(finalAmount);
            wrapper.setAmount(finalAmount);
            wrapper.setActualReceivedAmount(finalAmount);
            if (paymentType.getCode().equals("monthly_payment")) {
                Map<String, Object> m = calculateDiscountsWithVatService.getPaymentDiscountInfo(contract, paymentType, dto.getParsedDate(),
                        wrapper.isProrated(), false);
                wrapper.setIncludeWorkerSalary((Boolean) m.getOrDefault("includeWorkerSalary", false));
                wrapper.setInitial(contract.isMaidVisa() && !wrapper.isIncludeWorkerSalary());
                wrapper.setWorkerSalary((Double) m.getOrDefault("workerSalary", 0.0));
                wrapper.setDiscountAmount((Double) m.getOrDefault("discountAmount", 0.0));
            }
        } else {

            Map<String, Object> m = calculateDiscountsWithVatService.getPaymentDiscountInfo(contract, paymentType, dto.getParsedDate(),
                    wrapper.isProrated(), false);
            finalAmount = (Double) m.getOrDefault("amount", 0.0);
            if (finalAmount == 0) {
                throw new BusinessException("Amount is required for payment type: " + dto.getPaymentType());
            }

            Double additionalDiscount = (Double) m.getOrDefault("additionalDiscount", 0.0);
            Double moreAdditionalDiscount = (Double) m.getOrDefault("moreAdditionalDiscount", 0.0);
            logger.info("before discount | amount  : " + finalAmount + " , additionalDiscount : " + additionalDiscount +
                    " , moreAdditionalDiscount : " + moreAdditionalDiscount );

            finalAmount = (Double) calculateDiscountsWithVatService.getPaymentAmountAndAdditionalDiscount(additionalDiscount, finalAmount).get("amount");
            finalAmount = (Double) calculateDiscountsWithVatService.getAmountAfterApplyCreditNoteDiscount(moreAdditionalDiscount, finalAmount).get("amount");
            logger.info("after discount : " + finalAmount);
            wrapper.setAmount(finalAmount);
            wrapper.setActualReceivedAmount(finalAmount);
            wrapper.setAffectedByAdditionalDiscount(additionalDiscount > 0.0 || moreAdditionalDiscount > 0.0);
            wrapper.setIncludeWorkerSalary((Boolean) m.getOrDefault("includeWorkerSalary", false));
            wrapper.setInitial(contract.isMaidVisa() && !wrapper.isIncludeWorkerSalary());
            wrapper.setWorkerSalary((Double) m.getOrDefault("workerSalary", 0.0));
            wrapper.setDiscountAmount((Double) m.getOrDefault("discountAmount", 0.0));
            wrapper.setAdditionalDiscountAmount(additionalDiscount);
            wrapper.setMoreAdditionalDiscount(moreAdditionalDiscount);
        }
        todo.getContractPaymentList().add(wrapper);

        return todo;
    }

    private void callAfterPayFinsBGT(ContractPaymentConfirmationToDo t) {
        if (t.getContractPaymentTerm().getContract().getHousemaid() == null) {
            logger.info("housemaid id is null toDo id: " + t.getId());
            return;
        }
        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "afterPayFinsBGT_" + t.getId(),
                        "visa",
                        "visaGroupService",
                        "afterPayFins")
                        .withRelatedEntity(t.getContractPaymentTerm().getContract().getHousemaid().getEntityType(),
                                t.getContractPaymentTerm().getContract().getHousemaid().getId())
                        .withParameters(new Class[]{ Long.class },
                                new Object[]{ t.getContractPaymentTerm().getContract().getHousemaid().getId() })
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .build());
    }

    //ACC-8742
    public  Map<String, Object> createFromGPTAPIForExistingPayment(ExistingPaymentFromGPTDto dto) {
        Map<String, Object> result = new HashMap<>();
        String payTabsPaymentLink = "",reasonOfFailure = ""; boolean requestSuccessful = false;

        try {
            checkDTOValidationExistingPayment(dto);

            if (!QueryService.existsEntity(Payment.class, "e.id = :p0 and e.contract.id = :p1",
                    new Object[]{ Long.valueOf(dto.getSelectedPaymentID()), dto.getContractId() })) {
                throw new BusinessException("The Payment ID doesn't relate to the passed contract");
            }

            if (dto.selectedPaymentIsBounced() && !QueryService.existsEntity(Payment.class,
                    "e.id = :p0 and e.status = 'BOUNCED' and e.replaced = false",
                    new Object[]{Long.valueOf(dto.getSelectedPaymentID())})) {
                result.put("No_bounced_payment", true);
                throw new BusinessException("Bounced payment not found");
            }
            ContractPaymentConfirmationToDo toDo = prepareTodoForGPTExistingPayment(dto);

            validateCreation(toDo);

            Map<String, Object> m = validateCreateToDoFromErp(toDo);
            if(!m.get("valid").equals(true)) {
                if (m.containsKey("url"))
                    payTabsPaymentLink = (String) m.get("url");
                else
                    throw new BusinessException((String) m.getOrDefault("message", "Failed due to duplication with existing payment/link"));
            }

            if (payTabsPaymentLink.isEmpty()) {
                toDo = createToDoERPSource(toDo);

                if(toDo == null) {
                    throw new BusinessException("Unknown error");
                }
                payTabsPaymentLink = getPayingViaCreditCardLink(toDo);
            }
            requestSuccessful = true;
        } catch (Exception e) {
            e.printStackTrace();
            payTabsPaymentLink = "";
            reasonOfFailure = e.getMessage();
        }

        result.put("RequestSuccessful", requestSuccessful && !payTabsPaymentLink.isEmpty());
        result.put("Link", payTabsPaymentLink);
        result.put("ReasonOfFailure", reasonOfFailure);
        return result;
    }

    private void checkDTOValidationExistingPayment(ExistingPaymentFromGPTDto dto) {
        if (dto.getContractId() == null) throw new BusinessException("Contract ID is required");
        if (dto.getPaymentMethod() == null || dto.getPaymentMethod().isEmpty()) throw new BusinessException("Payment Method is required");
        if (dto.getSelectedPaymentID() == null) throw new BusinessException("Selected Payment ID is required");
        if (!PaymentMethod.CARD.toString().equals(dto.getPaymentMethod())) throw new BusinessException("Payment method should be CARD");
    }

    private ContractPaymentConfirmationToDo prepareTodoForGPTExistingPayment(ExistingPaymentFromGPTDto dto) {
        Contract contract = contractRepository.findOne(dto.getContractId());
        if (contract == null) throw new BusinessException("Contract Not Found");

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        Payment payment = paymentRepository.findOne(Long.valueOf(dto.getSelectedPaymentID()));
        if (payment == null) throw new BusinessException("Payment Not Found");

        logger.info("cpt id : " + cpt.getId());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();

        todo.setContractPaymentTerm(cpt);
        todo.setPaymentMethod(PaymentMethod.valueOf(dto.getPaymentMethod()));
        todo.setPayingOnline(dto.isPayingOnline());
        todo.setDescription("Added from GPT");
        todo.setPurpose(ContractPaymentConfirmationToDo.Purpose.NEW_GPT);
        todo.setPaymentType(payment.getTypeOfPayment());
        todo.setReplacementOfBouncedPayment(dto.selectedPaymentIsBounced());

        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setProrated(payment.getIsProRated());
        wrapper.setPaymentDate(payment.getDateOfPayment());
        wrapper.setPaymentType(payment.getTypeOfPayment());
        wrapper.setContractPaymentConfirmationToDo(todo);
        wrapper.setAmount(payment.getAmountOfPayment());
        wrapper.setDiscountAmount(payment.getDiscount());
        wrapper.setActualReceivedAmount(payment.getAmountOfPayment());
        wrapper.setInitial(payment.getIsInitial());
        wrapper.setIncludeWorkerSalary(payment.getIncludeWorkerSalary());
        if (dto.selectedPaymentIsBounced()) {
            wrapper.setReplacedBouncedPaymentId(payment.getId());
        }
        else {
            wrapper.setReplacedFuturePaymentId(payment.getId());
        }
        todo.getContractPaymentList().add(wrapper);

        return todo;
    }

    private void checkIfSourceIdChanged(ContractPaymentTerm cpt, EPaymentTransaction ePaymentTransaction, ContractPaymentConfirmationToDo t) {

        if (cpt.getSourceId() != null && cpt.getSourceId().equals(ePaymentTransaction.getSourceId())) return;
        logger.info("cpt id: " + cpt.getId());

        paymentService.removeRecurringPayment(cpt);

        ContractPaymentTermServiceNew contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        if (cpt.getContract().getStatus().equals(ContractStatus.ACTIVE)) {

            cpt = contractPaymentTermServiceNew.updateTokenOnCpt(
                    ePaymentTransaction.getSourceId(),
                    ePaymentTransaction.getTransactionType().equals(EpaymentTransactionType.AUTHORIZATION) ?
                        cpt.getContract().isOneMonthAgreement() ? cpt.getMonthlyPayment() : cpt.getDiscountedMonthlyPayment()
                        : ePaymentTransaction.getAmount(),
                    ePaymentTransaction.getSourceInfo(),
                    ePaymentTransaction.getProvider(),
                    cpt);
            contractPaymentTermRepository.save(cpt);

            paymentService.generateRecurringPayment(
                    t.getContractPaymentList().get(0), (LocalDate) cpt.getSourceInfo().get("expiryDate"), cpt);
        } else {
            contractPaymentTermRepository.save(contractPaymentTermServiceNew.removeTokenFromCpt(cpt));
        }

        List<ContractPaymentTerm> oldCpts = contractPaymentTermRepository.
                findContractPaymentTermWithNotNullSourceIDAndIsActiveFalseByContract(cpt.getContract());

        if (oldCpts.isEmpty()) return;

        oldCpts = oldCpts.stream().map(contractPaymentTermServiceNew::removeTokenFromCpt)
        .collect(Collectors.toList());
        contractPaymentTermRepository.save(oldCpts);
    }

    private void resolveErpTodoPayment(ContractPaymentConfirmationToDo t) throws Exception {
        unpaidOnlineCreditCardPaymentService.stopRunningFlows(t);
        unpaidOnlineCreditCardPaymentService.retractContractTermination(t);

        // ACC-4591 disable paytabs notification when client paid
        Setup.getApplicationContext()
                .getBean(DisableAccountingNotificationService.class)
                .disableOnClientPaidOnlinePaymentViaCard(t.getId());

        if (t.getSource().equals(Source.PAYMENT_REMINDER) && t.getRelatedEntityType() != null &&
                t.getRelatedEntityType().equals("OverstayFines")) {
            markOverstayFinesAsPaid(t);
        }

        if (t.getSource().equals(Source.PAYMENT_REMINDER) &&
                t.getContractPaymentList().stream().anyMatch( w -> w.getGeneratedPaymentId() == null)) {
            logger.info("payment reminder: " + t.getId());
            for (ContractPaymentWrapper w : t.getContractPaymentList()) {
                createPayment(w, t.getAttachments(), PaymentStatus.RECEIVED, true);
            }
            return;
        }

        //ACC-3856
        for (ContractPaymentWrapper w : t.getContractPaymentList()) {
            Long paymentId = null;

            if (w.getGeneratedPaymentId() != null) {
                paymentId = w.getGeneratedPaymentId();
            } else if (w.isReplacementOfFuturePayment()) {
                Payment actualPayment = paymentRepository.findOne(w.getReplacedFuturePaymentId());

                if (actualPayment != null && actualPayment.getMethodOfPayment() != null &&
                        !actualPayment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT) &&
                        actualPayment.getStatus().equals(PaymentStatus.PRE_PDP)) {

                    paymentId = actualPayment.getId();
                }
            } else {
                Payment payment = paymentRepository.findFirstByContractAndStatusAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                        t.getContractPaymentTerm().getContract(), PaymentStatus.PRE_PDP,
                        w.getAmount(), w.getPaymentMethod(), w.getPaymentType().getCode(),
                        new LocalDate(w.getPaymentDate()).withDayOfMonth(1).toDate(),
                        new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());
                paymentId = payment.getId();
            }

            logger.info("PAID Success contractPaymentWrapperID : " + w.getId() + " payment ID " + paymentId);
            changeCreditCardPaymentToReceived(paymentRepository.findOne(paymentId));
        }
    }

    public void changeCreditCardPaymentToReceived(Payment pdcPayment) {
        if (pdcPayment == null) throw new RuntimeException("Payment not created yet");
        pdcPayment.setStatus(PaymentStatus.RECEIVED);
        paymentService.forceUpdatePayment(pdcPayment);
        if (PaymentHelper.isMonthlyPayment(pdcPayment.getTypeOfPayment())) return;

        // ACC-6182
        Setup.getApplicationContext()
                .getBean(DirectDebitGenerationPlanService.class)
                .cancelledNonMonthlyPlanUponPaymentReceived(pdcPayment);
    }

    private void disableOnPaytabPaymentReceived(Contract c) {
        logger.log(Level.INFO, "Disable on paytap payment received");
        List<String> notificationCodeList = Arrays.asList(
                CcNotificationTemplateCode.CC_ACCOUNTING_PAY_VIA_CREDIT_CARD_NOTIFICATION.toString());

        List<PushNotification> notifications = Setup.getRepository(DisablePushNotificationRepository.class)
                .findActiveNotifications(c.getClient().getId().toString(), c.getId(), notificationCodeList);
        List<Long> notificationsId = notifications.stream().map(PushNotification::getId)
                .collect(Collectors.toList());

        logger.log(Level.INFO, "Notifications : found " + notificationsId.size());

        Setup.getApplicationContext().getBean(MessagingService.class)
                .createDisableNotificationBGT(notificationsId, "Disable on paytap payment received");
    }

    public ContractPaymentConfirmationToDo createMonthlyCreditCardForNextMonth(
            Contract contract,
            Source source) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        DateTime lastPayment = paymentService.getLastReceivedMonthlyPaymentDate(cpt.getContract());
        Date paymentDate = new LocalDate(lastPayment == null ? new DateTime() : lastPayment)
                .plusMonths(1)
                .withDayOfMonth(1)
                .toDate();


        return createMonthlyCreditCardForNextMonth(
                contract,
                paymentDate,
                source,
                null) ;
    }

    public ContractPaymentConfirmationToDo createMonthlyCreditCardForNextMonth(
            Contract contract,
            Date paymentDate,
            Source source,
            ContractPaymentWrapper additionPayment) {

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        logger.log(Level.INFO, "cpt id: {0}", cpt.getId());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
        todo.setContractPaymentTerm(cpt);
        todo.setSource(source);
        todo.setPaymentMethod(PaymentMethod.CARD);
        todo.setPayingOnline(true);

        ContractPaymentWrapper wrapper = createWrapperForMonthlyPayment(cpt, paymentDate);
        wrapper.setContractPaymentConfirmationToDo(todo);
        todo.setPaymentType(wrapper.getPaymentType());
        todo.setDescription(wrapper.getDescription());

        if (additionPayment != null) {
            additionPayment.setContractPaymentConfirmationToDo(todo);
            todo.getContractPaymentList().add(additionPayment);
        }
        todo.getContractPaymentList().add(wrapper);
        createConfirmationToDo(todo);

        return contractPaymentConfirmationToDoRepository.findOne(todo.getId());
    }

    public ContractPaymentWrapper createWrapperForMonthlyPayment(ContractPaymentTerm cpt, Date paymentDate) {

        ContractPaymentType monthlyPaymentType = cpt.getContractPaymentTypes().stream()
                .filter(c -> c.getType().getCode().equals(AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE))
                .findFirst().orElse(null);
        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setPaymentDate(paymentDate);
        wrapper.setProrated(false);
        wrapper.setInitial(false);
        wrapper.setDescription(monthlyPaymentType.getDescription());
        wrapper.setPaymentType(monthlyPaymentType.getType());
        wrapper.setSubType(monthlyPaymentType.getSubType());

        Map<String, Object> m = calculateDiscountsWithVatService
                .getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(paymentDate));
        wrapper.setAmount((Double) m.get("amount"));
        wrapper.setAffectedByAdditionalDiscount((Boolean) m.get("affectedByAdditionalDiscount"));
        // ACC-8422
        boolean includeWorkerSalary = m.containsKey("includeWorkerSalary") && (Boolean) m.get("includeWorkerSalary");
        wrapper.setIncludeWorkerSalary(includeWorkerSalary);
        wrapper.setWorkerSalary(includeWorkerSalary && m.containsKey("workerSalary") ? (Double) m.get("workerSalary") : 0D);
        wrapper.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));
        wrapper.setDiscountAmount((Double) m.getOrDefault("discountAmount", 0.0));

        return wrapper;
    }

    public ContractPayment createContractPaymentForMonthlyPayment(ContractPaymentTerm cpt, Date paymentDate) {

        ContractPaymentType monthlyPaymentType = cpt.getContractPaymentTypes().stream()
                .filter(c -> c.getType().getCode().equals("monthly_payment"))
                .findFirst().orElse(null);
        ContractPayment contractPayment = new ContractPayment();
        contractPayment.setDate(paymentDate);
        contractPayment.setDescription(monthlyPaymentType.getDescription());
        contractPayment.setPaymentType(monthlyPaymentType.getType());
        contractPayment.setSubType(monthlyPaymentType.getSubType());
        contractPayment.setContractPaymentTerm(cpt);

        Map<String, Object> m = Setup.getApplicationContext()
                .getBean(CalculateDiscountsWithVatService.class)
                .getAmountOfMonthlyPaymentAtTimeWithDiscounts(cpt, new LocalDate(paymentDate));
        contractPayment.setAmount((Double) m.get("amount"));
        contractPayment.setAdditionalDiscountAmount((Double) m.get("additionalDiscountAmountPerPayment"));
        contractPayment.setIncludeWorkerSalary((Boolean) m.get("includeWorkerSalary"));
        contractPayment.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));

        contractPayment.checkEntityBeforeInsert();

        return contractPayment;
    }

    @Transactional
    public ContractPaymentConfirmationToDo createConfirmationTodoFromContractPayments(
            ContractPaymentTerm cpt,
            List<ContractPayment> contractPayments,
            ContractPaymentConfirmationToDo.Source source) {

        return createConfirmationTodoFromContractPayments(
                cpt, contractPayments, source, new HashMap<>());
    }

    @Transactional
    public ContractPaymentConfirmationToDo createConfirmationTodoFromContractPayments(
            ContractPaymentTerm cpt,
            List<ContractPayment> contractPayments,
            ContractPaymentConfirmationToDo.Source source,
            Map<String, Object> map) {

        logger.info("create new confirmation todo cpt id: " + cpt.getId());

        ContractPaymentConfirmationToDo todo = new ContractPaymentConfirmationToDo();
        todo.setContractPaymentTerm(cpt);
        todo.setSource(source);
        todo.setPaymentType(contractPayments.get(0).getPaymentType());
        todo.setPaymentMethod(PaymentMethod.CARD);
        todo.setPayingOnline(true);
        todo.setDescription(contractPayments.get(0).getDescription());
        todo.setAttachments(contractPayments.get(0).getAttachments());
        todo.setRelatedEntityId((Long) map.getOrDefault("relatedToEntityId", null));
        todo.setRelatedEntityType((String) map.getOrDefault("relatedToEntityType", null));
        todo.setRequired((boolean)map.getOrDefault("required",
                UnpaidOnlineCreditCardPaymentService.isRequired(contractPayments)));
        todo.setCreditCardOffer((boolean)map.getOrDefault("creditCardOffer", false));

        contractPayments.forEach(cp -> {
            logger.info("contract payment: " + (cp.getId() == null ? "NUll" : cp.getId()));

            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            wrapper.setContractPaymentConfirmationToDo(todo);
            wrapper.setPaymentDate(cp.getDate());
            wrapper.setProrated(cp.getIsProRated());
            wrapper.setInitial(cp.getIsInitial());
            wrapper.setActualReceivedAmount(cp.getAmount());
            wrapper.setAmount(cp.getAmount());
            wrapper.setVatPaidByClient(cp.getVatPaidByClient());
            wrapper.setIncludeWorkerSalary(cp.getIncludeWorkerSalary());
            wrapper.setWorkerSalary(cp.getWorkerSalary());
            wrapper.setDescription(cp.getDescription());
            wrapper.setPaymentType(cp.getPaymentType());
            wrapper.setAffectedByAdditionalDiscount(cp.getAdditionalDiscountAmount() != null &&
                    cp.getAdditionalDiscountAmount() > 0.0);
            wrapper.setMoreAdditionalDiscount(cp.getMoreAdditionalDiscount());
            wrapper.setDiscountAmount(cp.getDiscountAmount());
            if (cp.getId() != null) {
                wrapper.setGeneratedPaymentId(paymentService.getGeneratedPaymentIdViaContractPaymentAndStatus(
                        cp, Arrays.asList(PaymentStatus.PDC, PaymentStatus.PRE_PDP)));
            } else if (map.containsKey("generatedPaymentId")) {
                wrapper.setGeneratedPaymentId((Long) map.get("generatedPaymentId"));
            }

            todo.getContractPaymentList().add(wrapper);
        });

        createConfirmationToDo(todo);
        return contractPaymentConfirmationToDoRepository.findOne(todo.getId());
    }

    public Payment addReplacementPayment(
            Payment payment,
            ContractPaymentWrapper w) {
        return addReplacementPayment(payment, w, false);
    }

    public Payment addReplacementPayment(
            Payment payment,
            ContractPaymentWrapper w,
            boolean ignorePostingEngineBR) {

        Payment replacementPayment = new Payment();
        replacementPayment.setAmountOfPayment(w.getActualReceivedAmount());
        replacementPayment.setDateOfPayment((java.sql.Date) w.getPaymentDate());
        replacementPayment.setContract(payment.getContract());
        replacementPayment.setIncludeWorkerSalary(w.isIncludeWorkerSalary());
        replacementPayment.setWorkerSalary(payment.getWorkerSalary());
        replacementPayment.setDiscount(w.getDiscountAmount()); // ACC-7964
        replacementPayment.setIsReplacement(Boolean.TRUE);
        replacementPayment.setReplacementFor(payment);
        replacementPayment.setMethodOfPayment(w.getContractPaymentConfirmationToDo().getPaymentMethod());
        replacementPayment.setStatus(PaymentStatus.RECEIVED);
        replacementPayment.setVatPaidByClient(w.isVatPaidByClient());
        replacementPayment.setIsInitial(w.isInitial());
        replacementPayment.setIsProRated(w.isProrated());
        replacementPayment.setNote(w.getNotes());
        replacementPayment.setOnline(w.getContractPaymentConfirmationToDo().isPayingOnline());
        replacementPayment.setBouncedPaymentId(payment.getId());
        replacementPayment.setParentWrapperId(w.getId()); // ACC-7697
        replacementPayment.setTypeOfPayment(w.getPaymentType());
        replacementPayment.setContractPaymentId(w.getContractPayment() != null ? w.getContractPayment().getId() : null);

//        // ACC-2568
//        if (w.getContractPaymentConfirmationToDo().getPaymentType() != null) {
//            replacementPayment.setTypeOfPayment(w.getContractPaymentConfirmationToDo().getPaymentType());
//        }

        if (ignorePostingEngineBR) { // ACC-8166
            replacementPayment.setIgnorePostingEngineBR(true);
        }

        if (payment.getSubType() != null) {
            replacementPayment.setSubType(payment.getSubType());
        }

        if (w.getContractPaymentConfirmationToDo().getAttachments() != null &&
                !w.getContractPaymentConfirmationToDo().getAttachments().isEmpty()) {
            replacementPayment.setAttachments(w.getContractPaymentConfirmationToDo().getAttachments());
        }

        replacementPayment = paymentService.replacePayment(replacementPayment, true);
        w.setGeneratedPaymentId(replacementPayment.getId());
        contractPaymentWrapperRepository.save(w);

        return replacementPayment;
    }

    public void sendWireTransferAmountDifferenceEmail(
            Contract c,
            Double amountDifference,
            List<ContractPaymentWrapper> paymentList) {

        if (paymentList != null && paymentList.size() > 0){
            logger.info("Id: " + paymentList.get(0).getId() +
                    "; Amount: " + paymentList.get(0).getAmount() +
                    "; actual amount: " + paymentList.get(0).getActualReceivedAmount());
        }

        Map<String, String> parameters = new HashMap();
        parameters.put("amount_difference", amountDifference.toString());
        String payment_list = "";
        for (ContractPaymentWrapper payment : paymentList){
            payment_list += "Payment Id: " + (payment.getReplacedFuturePaymentId() == null ? payment.getGeneratedPaymentId() : payment.getReplacedFuturePaymentId()) +
                    ", Amount: " + payment.getAmount() + ", Actual Received Amount: " + payment.getActualReceivedAmount() + "<br/>";
        }
        parameters.put("payment_list", payment_list);
        try {
            //ACC-8747
            Map<String, String> emailsRecipientObject = objectMapper.readValue(Setup.getParameter(Setup.getCurrentModule(),
                    AccountingModule.PARAMETER_MISSING_TRANSFER_AMOUNT_FOR_CLIENT_ALERT_RECIPIENTS), Map.class);
            String emailsRecipient = c.isMaidVisa() ?
                    emailsRecipientObject.get("clientMV") :
                    c.getLiveOut() ?
                            emailsRecipientObject.get("clientCCLiveOut") :
                            emailsRecipientObject.get("clientCCLiveIn");

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("wire_transfer_amount_difference",
                            parameters, emailsRecipient, String.format("Missing transfer amount for client (%s)", c.getClient().getName()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //ACC-4715
    public String getPaymentsDetails(ContractPaymentConfirmationToDo toDo) {

        logger.log(Level.INFO, "todo id : {0}", toDo.getId());
        Map<String, String> result = new HashMap<>();
        StringBuilder output = new StringBuilder();
        result.put("currentMonthPayment", "");
        result.put("vattedPayments", "");
        result.put("paymentsWithoutVAT", "");
        String workerType = (toDo.getContractPaymentTerm().getContract().getWorkerType() != null &&
                toDo.getContractPaymentTerm().getContract().getWorkerType().getCode().equals("private_driver") ?
                "Driver" : "Maid")
                + "'s salary";

        final int[] index = {1};

        // Sorted By 1-(Payment Date) Then by 2-(ID of Payment Type)
        toDo.getContractPaymentList().stream()
                .sorted(Comparator.comparing(ContractPaymentWrapper::getPaymentDate)
                        .thenComparingLong(w -> w.getPaymentType().getId()))
                .forEach(wrapper -> {

                    if (index[0] == 1 ) {
                        result.put("currentMonthPayment", new DateTime(wrapper.getPaymentDate()).toString("MMM"));
                        output.append("For " + result.get("currentMonthPayment") + " ");
                    }

                    Payment payment = wrapper.getGeneratedPaymentId() != null ?
                            paymentRepository.findOne(wrapper.getGeneratedPaymentId()) :
                            null;

                    Double workerSalary = payment != null ?
                            payment.getWorkerSalary() :
                            toDo.getContractPaymentTerm().getContract().getWorkerSalaryNew();

                    if (workerSalary == null) {
                        workerSalary = 0D;
                    }

                    Double a = PaymentHelper.isMonthlyPayment(wrapper.getPaymentType()) && wrapper.isIncludeWorkerSalary() ?
                            DiscountsWithVatHelper.getAmountWithoutVat((wrapper.getAmount() - workerSalary)) :
                            wrapper.getPaymentType().hasTag(ContractPayment.NO_VAT_TAG) ?
                                    wrapper.getAmount() :
                                    DiscountsWithVatHelper.getAmountWithoutVat(wrapper.getAmount());

                    String description = wrapper.isIncludeWorkerSalary() ? workerType + " + " : "";
                    description += "AED " + Double.valueOf(Math.floor(a)).intValue();

                    if (wrapper.isProrated()) {
                        AtomicReference<Integer> amount = new AtomicReference<>(wrapper.getAmount().intValue());
                        toDo.getContractPaymentTerm().getContractPaymentTypes()
                                .stream()
                                .filter(contractPaymentType -> PaymentHelper.isMonthlyPayment(contractPaymentType.getType()))
                                .findFirst()
                                .ifPresent(c -> amount.set(Double.valueOf(c.getAmount()).intValue()));
                        amount.set(Double.valueOf(Math.floor(DiscountsWithVatHelper.getAmountWithoutVat(amount.get()))).intValue());

                        int n = Days.daysBetween(new DateTime(wrapper.getPaymentDate()),
                                new DateTime(wrapper.getPaymentDate()).dayOfMonth().withMaximumValue()).getDays() + 1;
                        int lastDayOfMonth = new DateTime(wrapper.getPaymentDate()).dayOfMonth().withMaximumValue().getDayOfMonth();
                        description += "(" + amount.get() + "/" + lastDayOfMonth + ")*" + n;
                    } else if (!PaymentHelper.isMonthlyPayment(wrapper.getPaymentType())) {
                        ContractPaymentType type = toDo.getContractPaymentTerm().getContractPaymentTypes()
                                .stream()
                                .filter(t -> t.getType().getCode().equals(wrapper.getPaymentType().getCode()))
                                .findFirst()
                                .orElse(null);
                        description += " " + (type != null && type.getDescription() != null && !type.getDescription().isEmpty()?
                                type.getDescription() :
                                wrapper.getDescription() != null && !wrapper.getDescription().isEmpty() ?
                                        wrapper.getDescription() :
                                        wrapper.getPaymentType().getName());
                    }

                    if (!result.get("currentMonthPayment").equalsIgnoreCase(new DateTime(wrapper.getPaymentDate()).toString("MMM"))) {
                        result.put("currentMonthPayment", new DateTime(wrapper.getPaymentDate()).toString("MMM"));

                        addLastPaymentToPaymentsDetails(result, output);

                        result.put("vattedPayments", "");
                        result.put("paymentsWithoutVAT", "");

                        output.append(", For " + result.get("currentMonthPayment") + " ");
                    }

                    String key = wrapper.getPaymentType().hasTag(ContractPayment.NO_VAT_TAG) ? "paymentsWithoutVAT" : "vattedPayments";
                    result.put(key, result.get(key).isEmpty() ? description : result.get(key) + " + " + description);

                    if (index[0] == toDo.getContractPaymentList().size()) {
                        addLastPaymentToPaymentsDetails(result, output);
                    }
                    index[0]++;
                });


        logger.info("output : " + output);
        return output.toString().trim();
    }

    private void addLastPaymentToPaymentsDetails(Map<String, String> result, StringBuilder output) {
        if (!result.get("vattedPayments").isEmpty()) {
            output.append("(" + result.get("vattedPayments") + " + 5% VAT)");
        }

        if (!result.get("vattedPayments").isEmpty() && !result.get("paymentsWithoutVAT").isEmpty()) {
            output.append(" + ");
        }

        if (!result.get("paymentsWithoutVAT").isEmpty()) {
            output.append("(" + result.get("paymentsWithoutVAT") + ")");
        }
    }

    public Map<String, Object> confirmOnlineCardPayment(ContractPaymentWrapper w) {

        Map<String, Object> result = new HashMap<>();
        Payment newPayment = null;

        if (w.isReplacementOfBouncedPayment() && w.getGeneratedPaymentId() != null) {
            logger.info("Replacement of Bounced Payment -> call replacing payment API from ClientMgmt Module");

            // call update from client mgmt
            newPayment = paymentRepository.findOne(w.getGeneratedPaymentId());
            if (newPayment != null) {
                newPayment.setIsReplacement(true);
                newPayment.setReplacementFor(paymentRepository.findOne(w.getReplacedBouncedPaymentId()));
            }
        } else if (w.isReplacementOfFuturePayment()) {
            logger.info("Replacement of Future Payment -> call replacing payment API from ClientMgmt Module");

            if (w.getGeneratedPaymentId() != null) {
                newPayment = paymentRepository.findOne(w.getGeneratedPaymentId());
            } else {
                newPayment = paymentRepository.findOne(w.getReplacedFuturePaymentId());
            }
        } else if (w.isCreationOfNewPayment() && w.getGeneratedPaymentId() != null) {
            newPayment = paymentRepository.findOne( w.getGeneratedPaymentId());
        }

        if(newPayment == null) {
            newPayment = paymentRepository.findFirstByContractAndStatusAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                    w.getContractPaymentConfirmationToDo().getContractPaymentTerm().getContract(),
                    PaymentStatus.RECEIVED, w.getAmount(), w.getPaymentMethod(), w.getPaymentType().getCode(),
                    new LocalDate(w.getPaymentDate()).withDayOfMonth(1).toDate(),
                    new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());

            if(newPayment == null) {
                newPayment = paymentRepository.findFirstByContractAndAmountOfPaymentAndMethodOfPaymentAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                        w.getContractPaymentConfirmationToDo().getContractPaymentTerm().getContract(),
                        w.getAmount(), w.getPaymentMethod(), w.getPaymentType().getCode(),
                        new LocalDate(w.getPaymentDate()).withDayOfMonth(1).toDate(),
                        new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());
            }
        }

        if (newPayment == null || PaymentStatus.DELETED.equals(newPayment.getStatus())) return result;

        newPayment.setStatus(PaymentStatus.RECEIVED);
        newPayment.setAttachments(w.getContractPaymentConfirmationToDo().getAttachments());

        if(newPayment.getId() != null){

            newPayment = paymentService.forceUpdatePayment(newPayment);

            result.put("payment", newPayment);
            result.put("transaction", paymentService.createTransactionForPayment(newPayment));// ACC-5147
        }

        return result;
    }

    public Payment confirmNonOnlineAndCashPayment(ContractPaymentWrapper w) throws Exception {
        Long generatedPayment = null;
        Payment newPayment = null;

        if (w.isReplacementOfBouncedPayment()) {
            logger.info("Replacement of Bounced Payment -> call replacing payment API from ClientMgmt Module");
            Payment bouncedPayment = paymentRepository.findOne(w.getReplacedBouncedPaymentId());
            generatedPayment = addReplacementPayment(bouncedPayment, w).getId();

        } else if (w.isReplacementOfFuturePayment()) {
            logger.info("Replacement of Future PRE_PDP/PDP Payment -> call receiving Payment API from ClientMgmt Module");
            Payment actualPayment = paymentRepository.findOne(w.getReplacedFuturePaymentId());

            if(actualPayment.getMethodOfPayment() != null && actualPayment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) {
                logger.info("Creation of new Payment -> call creating new Payment API from ClientMgmt Module");
                generatedPayment = createPayment(w, w.getContractPaymentConfirmationToDo().getAttachments(), PaymentStatus.RECEIVED, false);
            } else{
                newPayment = actualPayment;
                newPayment.setStatus(PaymentStatus.RECEIVED);
                newPayment.setAttachments(w.getContractPaymentConfirmationToDo().getAttachments());

                paymentService.forceUpdatePayment(newPayment);
            }
        } else if (w.isCreationOfNewPayment()) {
            logger.info("Creation of new Payment -> call creating new Payment API from ClientMgmt Module");
            generatedPayment = createPayment(w, w.getContractPaymentConfirmationToDo().getAttachments(), PaymentStatus.RECEIVED, false);
        }

        // ACC-6411
        if (generatedPayment != null ) {
            newPayment = paymentRepository.findOne(generatedPayment);
        }

        return newPayment;
    }

    public Map<String, Object> confirmNonOnlinePaymentAndCreateTransaction(ContractPaymentWrapper w) throws Exception {
        logger.info("wrapper id: " + w.getId());
        Payment newPayment = confirmNonOnlineAndCashPayment(w);

        if (newPayment == null) return null;
        Map<String, Object> generatedPayment = new HashMap<>();
        generatedPayment.put("payment", newPayment);
        generatedPayment.put("transaction", paymentService.createTransactionForPayment(newPayment));

        return generatedPayment;
    }

    public void validateCreation(ContractPaymentConfirmationToDo toDo) {
        Set<String> coveredPayments = new HashSet<>();
        if (toDo.getContractPaymentList() == null || toDo.getContractPaymentList().isEmpty()) {
            throw new RuntimeException("Error: You must add at least one payment");
        }

        for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {
            Long paymentId = w.getReplacedBouncedPaymentId() != null ? w.getReplacedBouncedPaymentId() : w.getReplacedFuturePaymentId();

            if(paymentId != null) {
                Payment payment = paymentRepository.findOne(paymentId);
                if (payment.getDirectDebitFileId() != null) {
                    DirectDebitFile ddf = directDebitFileRepository.findOne(payment.getDirectDebitFileId());
                    if (ddf != null && DirectDebitFileSubStatus.SENT_FOR_COLLECTION.equals(DirectDebitFileService.getSubStatus(ddf))) {
                        throw new BusinessException("You cannot select this payment since it was sent for collection and is waiting for the bank's response.");
                    }
                }
                toDo.setContractPaymentTerm(payment.getContract().getActiveContractPaymentTerm());
                toDo.setPaymentType(payment.getTypeOfPayment());

                w.setPaymentDate(payment.getDateOfPayment());
                w.setAmount(payment.getAmountOfPayment());
                w.setPaymentType(payment.getTypeOfPayment());
                w.setSubType(payment.getSubType());
            } else if (toDo.getPaymentType() == null && w.getPaymentType() != null) {
                toDo.setPaymentType(picklistItemRepository.findOne(w.getPaymentType().getId()));
            }
        }

        if(toDo.getContractPaymentTerm() == null
                && toDo.getContract() != null && toDo.getContract().getId() != null) {

            Contract contract = contractRepository.findOne(toDo.getContract().getId());
            toDo.setContractPaymentTerm(contract.getActiveContractPaymentTerm());

        } else if (toDo.getContractPaymentTerm() != null && toDo.getContractPaymentTerm().getId() != null){
            toDo.setContractPaymentTerm(contractPaymentTermRepository.findOne(toDo.getContractPaymentTerm().getId()));
        }

        for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {
            if (w.isReplacementOfFuturePayment() || w.isCreationOfNewPayment()) {
                String key = new LocalDate(w.getPaymentDate()).withDayOfMonth(1).toString("yyyy-MM-dd") +
                        "_" + w.getPaymentType().getId();

                if (coveredPayments.contains(key)) {
                    throw new RuntimeException("Error: there are more than 1 monthly payment defined for the same month and type");
                } else {
                    coveredPayments.add(key);
                }
            }
        }
    }

    @Transactional
    public ContractPaymentConfirmationToDo createConfirmationToDo(ContractPaymentConfirmationToDo t) {
        logger.info("Payment Method: " + t.getPaymentMethod() +
                "; Payment Method: " + t.getPaymentType() +
                "; Source: " + t.getSource());
        if (t.getTotalAmount() <= 0) return null;

        for (ContractPaymentWrapper w : t.getContractPaymentList()) {
            logger.info("INITIAL: " + w.isInitial());
            if (w.isIncludeWorkerSalary() && (w.getWorkerSalary() == null || w.getWorkerSalary() == 0D) && t.getContractPaymentTerm() != null) {
                w.setWorkerSalary(t.getContractPaymentTerm().getContract().getWorkerSalaryNew());
                if (w.getDiscountAmount() == 0.0) {
                    w.setDiscountAmount(t.getContractPaymentTerm().getDiscount());
                }
            }

            if (w.isReplacementOfBouncedPayment()) {
                logger.info("Replacement Of Bounced Payment");

                Payment bouncedPayment = paymentRepository.findOne(w.getReplacedBouncedPaymentId());

                //CMA-2112 mark the push notification as disabled in case B or D
                if (t.getNotificationId() != null
                        && t.getSelectedCase() != null
                        && (t.getSelectedCase().equals(1) || t.getSelectedCase().equals(3))) {

                    PushNotificationRepository pushNotificationRepository = Setup.getRepository(PushNotificationRepository.class);
                    PushNotification notification = pushNotificationRepository.findOne(t.getNotificationId());
                    if (notification != null) {
                        notification.setReceived(true);
                        notification.setDisabled(true);
                        notification.setLocation(NotificationLocation.INBOX);
                        pushNotificationRepository.save(notification);
                    }
                }

                w.updateFromPayment(bouncedPayment);
                w.generateNewContractPayment();

                if(!(t.getPaymentMethod().equals(PaymentMethod.CARD) && t.isPayingOnline() &&
                        Arrays.asList(
                                ContractPaymentConfirmationToDo.Source.ERP,
                                Source.BOUNCED_PAYMENT_FLOW)
                                .contains(t.getSource()))) {
                    logger.info("Replacement Of Bounced Payment");
                    paymentService.pauseBouncingFlow(w.getReplacedBouncedPaymentId());
                }
            } else if (w.isReplacementOfFuturePayment()) {
                logger.info("Replacement Of Future Payment");
                Payment actualPayment = paymentRepository.findOne(w.getReplacedFuturePaymentId());
                w.updateFromPayment(actualPayment);
                w.generateNewContractPayment();
            } else if (w.isCreationOfNewPayment() && w.getContractPayment() == null) {
                logger.info("Creation Of New Payment");

                w.generateNewContractPayment();
                w.updateFromContractPayment();
            }
        }

        // ACC-3863
        String typesOfPayment = t.getContractPaymentList().stream().
                collect(Collectors.groupingBy(ContractPaymentWrapper::getPaymentType))
                .keySet().stream().map(picklistItem -> picklistItem.getId())
                .map(String::valueOf).collect(Collectors.joining(","));

        t.setTypesOfPayments(typesOfPayment);
        t.setShowOnERP(!t.getPaymentMethod().equals(PaymentMethod.CARD) ||
                !t.isPayingOnline() ||
                t.getSource().equals(Source.OTHER) ||
                t.getSource().equals(Source.CC_APP) ||
                t.getSource().equals(Source.INITIAL_PAYMENTS));

        if (t.getPaymentMethod().equals(PaymentMethod.CARD) && t.isPayingOnline() && t.isShowOnERP()) {
            t.setCardPaymentReceivedDate(new Date());
        }

        if (t.getSource().equals(Source.ERP) && t.getPaymentType() == null)
            t.setPaymentType(picklistItemRepository.findOne(t.getContractPaymentList().get(0).getId()));

        t = contractPaymentConfirmationToDoRepository.save(t);

        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(t.getContractPaymentTerm().getId());
        logger.info("todo ID is " + t.getId() + "; cpt id: {0}" + cpt.getId());

        if (t.getPaymentMethod().equals(PaymentMethod.CARD) && t.isPayingOnline()) {
            if (t.getSource().equals(Source.ERP)) {
                //sendPayTabsLinkViaMessage(t);

                unpaidOnlineCreditCardPaymentService.createPaymentReminderFlow(t, cpt);
            }

            if (t.getSource().equals(Source.ERP) ||
                    t.getSource().equals(Source.OTHER) ||
                    t.getSource().equals(Source.CC_APP) ||
                    t.getSource().equals(Source.INITIAL_PAYMENTS) ||
                    t.getSource().equals(Source.PAYMENT_REMINDER)) {

                //ACC-3856
                for (ContractPaymentWrapper w : t.getContractPaymentList()) {
                    logger.info("Creation of new Payment -> call creating new Payment API from ClientMgmt Module");

                    if(w.isReplacementOfFuturePayment()) {
                        logger.info("Future payment ID: " + w.getReplacedFuturePaymentId());
                        Payment futurePayment = paymentRepository.findOne(w.getReplacedFuturePaymentId());

                        if(futurePayment.getMethodOfPayment() != null &&
                                !PaymentMethod.DIRECT_DEBIT.equals(futurePayment.getMethodOfPayment()) &&
                                (!PaymentMethod.CARD.equals(futurePayment.getMethodOfPayment()) || !futurePayment.isOnline())) {

                            // No need to create new payment
                            logger.info("createFromErpAPI in if isReplacementOfFuturePayment && payment method not DIRECT_DEBIT");
                            continue;
                        }
                    } else if(w.isReplacementOfBouncedPayment() && t.getSource().equals(Source.CC_APP)) {
                        Payment bouncedPayment = paymentRepository.findOne(w.getReplacedBouncedPaymentId());
                        addReplacementPayment(bouncedPayment, w);
                        continue;
                    }

                    logger.info("createFromErpAPI create new payment");
                    if (w.getGeneratedPaymentId() == null) {
                        createPayment(w, t.getAttachments(),
                                Arrays.asList(Source.ERP, Source.PAYMENT_REMINDER).contains(t.getSource()) ?
                                 PaymentStatus.PRE_PDP : PaymentStatus.RECEIVED,
                                true);
                    }
                }
            }
        } else{
            t.setPayingOnline(false);
        }

        if(Arrays.asList(
                Source.CC_APP,
                Source.BOUNCED_PAYMENT_FLOW,
                Source.FAQ,
                Source.SWITCH_NATIONALITY,
                Source.VISA_OVERSTAY_FEE)
                .contains(t.getSource())) {
            Setup.getApplicationContext().getBean(CCAppService.class).insertTrackingLog(
                    t.getContractPaymentTerm().getContract().getUuid(), CcAppTracking.CcAppAction.PAY_BY_CARD);
        }

        removeMatchedToDosByOldContracts(t);

        deleteTokenizedPdpPayments(t);

        return contractPaymentConfirmationToDoRepository.save(t);
    }

    public Long createPayment(
            ContractPaymentWrapper w,
            List<Attachment> attachments,
            PaymentStatus status,
            boolean forceCreate) {

        return createPayment(w, attachments, status, forceCreate, false);
    }

    public Long createPayment(
            ContractPaymentWrapper w,
            List<Attachment> attachments,
            PaymentStatus status,
            boolean forceCreate,
            boolean ignorePostingEngineBR) {

        if (w.getContractPayment() == null) throw new RuntimeException("Payment not Found");

        ContractPayment contractPayment = contractPaymentRepository
                .findOne(w.getContractPayment().getId());
        w.setContractPayment(contractPayment);
        if(w.getVatPaidByClient() == null) {
            w.setVatPaidByClient(contractPayment.getVatPaidByClient());
        }

        //Payment Map
        logger.info("contractPayment Id: " + contractPayment.getId());
        logger.info("ContractPaymentTerm Id: " + contractPayment.getContractPaymentTerm().getId());
        logger.info("Contract Id: " + contractPayment.getContractPaymentTerm().getContract().getId());
        logger.info("Initial: " + contractPayment.getIsInitial());
        logger.info("IsProRated: " + contractPayment.getIsProRated());

        Payment p = new Payment();
        p.setTypeOfPayment(contractPayment.getPaymentType());

        Contract contract = contractPayment.getContractPaymentTerm() != null ? contractPayment.getContractPaymentTerm().getContract() : null;
        if (contract == null) throw new RuntimeException("Contract not Found");

        p.setContract(contract);
        p.setAmountOfPayment( w.getActualReceivedAmount());
        p.setVatPaidByClient(w.getVatPaidByClient());
        p.setIsInitial(w.isInitial());
        p.setIsProRated(w.isProrated());
        p.setIncludeWorkerSalary(w.isIncludeWorkerSalary());
        if (w.isIncludeWorkerSalary()) { p.setWorkerSalary(w.getWorkerSalary()); }
        p.setDiscount(w.getDiscountAmount()); // ACC-7964
        p.setDateOfPayment(java.sql.Date.valueOf(new LocalDate(contractPayment.getDate()).toString("yyyy-MM-dd")));
        p.setStatus(status);
        p.setMethodOfPayment(contractPayment.getPaymentMethod());
        p.setNote(w.getNotes());
        p.setParentWrapperId(w.getId());
        p.setManuallyCreated(w.getContractPaymentConfirmationToDo().getSource().equals(Source.ERP)); // CM-1723
        p.setOnline(w.getContractPaymentConfirmationToDo().isPayingOnline());
        p.setContractPaymentId(w.getContractPayment() != null ? w.getContractPayment().getId() : null);

        if (ignorePostingEngineBR) { // ACC-8166
            p.setIgnorePostingEngineBR(true);
        }
        if (contractPayment.getSubType() != null) {
            p.setSubType(contractPayment.getSubType());
        }

        if (attachments != null && !attachments.isEmpty()) {
            p.setAttachments(attachments.stream().map(a -> Storage.cloneTemporary(a, a.getTag())).collect(Collectors.toList()));
        }

        p = paymentService.createPayment(p, forceCreate);
        if (p == null) return null;

        w = Setup.getRepository(ContractPaymentWrapperRepository.class).findOne(w.getId());
        w.setGeneratedPaymentId(p.getId());
        Setup.getRepository(ContractPaymentWrapperRepository.class).save(w);

        logger.info(String.format("New Payment ID is (%s)", p.getId()));
        return p.getId();
    }

    /*public void createPaymentReminderTodo(Contract contract) {
        logger.log(Level.INFO, "contract ID: {0}", contract.getId());
        Map requestBody = new HashMap();
        requestBody.put("reasonToCall", "Unpaid credit card payment");
        requestBody.put("initialNote", "");
        requestBody.put("type", VoiceResolverToDoReason.PAYMENT_REMINDER.toString());

        moduleConnector.postJson("/clientmgmt/voiceResolverToDo/createexperttodo/"
                + contract.getId(), requestBody, Map.class);
    }*/

    // ACC-4591 ACC-3843
    public void sendPayTabsLinkViaMessage(ContractPaymentConfirmationToDo toDo) {
        logger.info("toDo id: " + toDo.getId());

        Map<String, String> params = new HashMap<String, String>() {{
            put("greetings", toDo.getContractPaymentTerm().getContract().isMaidCc() ?
                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                    Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
            put("amount", String.valueOf(toDo.getTotalAmount().intValue()));
            put("paytabs_link", getPayingViaCreditCardLink(toDo));
        }};

        AppAction a = Setup.getApplicationContext()
                .getBean(FlowProcessorMessagingService.class)
                .getPaytabsButton(
                        new HashMap<String, String>() {{
                            put("todoUuid", toDo.getUuid());
                            put("amount", String.valueOf(toDo.getTotalAmount().intValue()));
                        }},
                        toDo.getContractPaymentTerm().getContract());

        Template t = TemplateUtil.getTemplate(toDo.getContractPaymentTerm().getContract().isMaidCc() ?
                CcNotificationTemplateCode.CC_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString() :
                MvNotificationTemplateCode.MV_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString());

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendMessageToClient(toDo.getContractPaymentTerm().getContract(),
                        params,
                        new HashMap<String, AppAction>() {{
                            put("payment_credit_card", a);
                        }},
                        toDo.getId(),
                        toDo.getEntityType(),
                        t);
    }

    public void disablePayTabLinksBySource(Contract c, ContractPaymentConfirmationToDo.Source source) {
        disablePayTabLinksBySource(c, Collections.singletonList(source));
    }

    public void disablePayTabLinksBySource(Contract c, List<ContractPaymentConfirmationToDo.Source> sources) {

        contractPaymentConfirmationToDoRepository.findByContractPaymentTerm_ContractAndSourceInAndShowOnERPFalseAndDisabledFalse(
                c, sources)
                .forEach(d -> {
                    logger.info("todo id: " + d.getId());
                    d.setDisabled(true);
                    contractPaymentConfirmationToDoRepository.save(d);
                });

        disablePayTabLinksNotificationsBySource(c, sources);
    }

    public void disablePayTabLinksNotificationsBySource(Contract c, List<ContractPaymentConfirmationToDo.Source> sources) {

        sources.forEach(s -> {
            switch (s) {
                case ONE_MONTH_AGREEMENT:
                    oneMonthAgreementFlowService.disableOldNotification(c);
                    break;
                case CLIENT_PAYING_VIA_Credit_Card:
                    clientPayingViaCreditCardService.disableOldNotification(c);
                    break;
                case AFTER_CASH_FLOW:
                    afterCashFlowService.disableOldNotification(c);
                    break;
            }
        });
    }

    public void disableAllPayTabLinks(Contract c) {
        contractPaymentConfirmationToDoRepository.findByContractPaymentTerm_ContractAndShowOnERPFalseAndDisabledFalse(c)
                .forEach(d -> {
                    logger.info("todo id: " + d.getId());
                    d.setDisabled(true);
                    contractPaymentConfirmationToDoRepository.save(d);
                });
    }

    // ACC-7189
    private void removeMatchedToDosByOldContracts(ContractPaymentConfirmationToDo toDo) {

        if (toDo.getOldContracts() == null || toDo.getOldContracts().isEmpty() || toDo.getTransferReference() == null) return;

        logger.info("oldContracts: " + toDo.getOldContracts());
        List<Long> contractsIds = Arrays.stream(toDo.getOldContracts().split(","))
                .map(Long::valueOf).collect(Collectors.toList());

        //ACC-8352
        List<ContractPaymentConfirmationToDo> todosByOldContracts = contractPaymentConfirmationToDoRepository
                .getByContractAndTransferReference(
                        toDo.getId(), contractsIds, toDo.getTransferReference());
        logger.info("todosByOldContracts to delete before filter: " + todosByOldContracts.size());

        Double totalAmount = toDo.getTotalAmount();
        logger.info("totalAmount: " + totalAmount);
        todosByOldContracts = todosByOldContracts.stream()
                .filter(t -> {
                    Double tTotalAmount = t.getTotalAmount();
                    logger.info("tTotalAmount: " + tTotalAmount + "; t id: " + t.getId());
                    return tTotalAmount.equals(totalAmount);
                }).collect(Collectors.toList());

        logger.info("todosByOldContracts to delete: " + todosByOldContracts.size());

        todosByOldContracts.forEach(todo -> {
            logger.info("previous todo id: " + todo.getId());
            contractPaymentConfirmationToDoRepository.delete(todo);
        });
    }

    public boolean isTodoExpired(ContractPaymentConfirmationToDo toDo) {
        return toDo != null && (toDo.isDisabled() ||
                (!toDo.isReactivationPayment() && toDo.getContractPaymentTerm().getContract().isEnded() &&
                        toDo.getContractPaymentTerm().getContract().getDateOfTermination() != null &&
                        toDo.getContractPaymentTerm().getContract().getDateOfTermination().getTime() >
                                toDo.getCreationDate().getTime()));

    }

    public String getPayingViaCreditCardLink(String toDoUuid) {
        return getPayingViaCreditCardLink(contractPaymentConfirmationToDoRepository.findByUuid(toDoUuid), false, false);
    }

    public String getPayingViaCreditCardLink(ContractPaymentConfirmationToDo toDo) {
        return getPayingViaCreditCardLink(toDo, false, false);
    }

    public String getPayingViaCreditCardLink(ContractPaymentConfirmationToDo toDo, Boolean authorization, Boolean recurring) {

        String link = Setup.getCoreParameter(CoreParameter.PAYMENT_PUBLIC_PAGE) +
                "/accounting/accountingEPaymentService?context=%7B%22todoUuid%22:%22" + toDo.getUuid() + "%22%7D" +
                (authorization ? "&authorization=true" : "") +
                (recurring ? "&recurring=true" : "");

        AccountingLink a = AccountingLinkService.getOrCreateNewLink(
                new AccountingLink.AccountingLinkBuilder()
                        .AccountingLink(toDo.getId(),
                                toDo.getEntityType(),
                                AccountingLink.AccountingLinkType.ONLINE_CARD,
                                link)
                        .setContractId(toDo.getContractPaymentTerm().getContract().getId())
                        .setCptId(toDo.getContractPaymentTerm().getId()));

        return a.getShortenedLink();
    }

    @Transactional
    public ContractPaymentConfirmationToDo createToDoForSwitchNationalityPurpose(
            List<Map<String, Object>> l, ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Purpose purpose) {

        try {
            logger.log(Level.INFO,"Cpt id: {0}", cpt.getId());

            ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
            toDo.setContractPaymentTerm(cpt);
            toDo.setSource(Source.SWITCH_NATIONALITY);
            toDo.setPaymentMethod(PaymentMethod.CARD);
            toDo.setPayingOnline(true);
            toDo.setPurpose(purpose);

            List<ContractPaymentWrapper> wrappers = l.stream()
                    .map(m -> {
                        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
                        wrapper.setContractPaymentConfirmationToDo(toDo);
                        wrapper.setPaymentDate(DateTimeFormat.forPattern("yyyy-MM-dd 00:00:00")
                                .parseLocalDate((String) m.get("paymentDate")).toDate());
                        wrapper.setActualReceivedAmount((Double) m.get("amount"));
                        wrapper.setAmount((Double) m.get("amount"));
                        wrapper.setPaymentType(picklistItemRepository.findOne(Long.valueOf((Integer) ((Map<String, Object>)m.get("paymentType")).get("id"))));
                        ContractPaymentType type = cpt.getContractPaymentTypes()
                                .stream()
                                .filter(t -> t.getType().getCode().equals(wrapper.getPaymentType().getCode()))
                                .findFirst()
                                .orElse(null);
                        wrapper.setDescription(type != null ? type.getDescription() : wrapper.getPaymentType().getName());
                        wrapper.setAffectedByAdditionalDiscount((Boolean) m.getOrDefault("affectedByAdditionalDiscount", false));
                        wrapper.setIncludeWorkerSalary((Boolean) m.getOrDefault("includeWorkerSalary", false));
                        // ACC-8422
                        Double workerSalary = (Double) m.getOrDefault("workerSalary", null);
                        if (wrapper.isIncludeWorkerSalary() && workerSalary != null) {
                            wrapper.setWorkerSalary(workerSalary);
                        }
                        wrapper.setMoreAdditionalDiscount((Double) m.getOrDefault("moreAdditionalDiscount", 0.0));
                        wrapper.setIncludeUpgradingFee((Boolean) m.getOrDefault("includeUpgradingFee", false));

                        return wrapper; })
                    .collect(Collectors.toList());

            toDo.setContractPaymentList(wrappers);
            toDo.setDescription(wrappers.get(0).getDescription());
            toDo.setPaymentType(wrappers.get(0).getPaymentType());

            Setup.getApplicationContext()
                    .getBean(ContractPaymentConfirmationToDoService.class)
                    .createConfirmationToDo(toDo);
            return contractPaymentConfirmationToDoRepository.findOne(toDo.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Transactional
    public ContractPaymentConfirmationToDo createToDoForVisaOverStayFeePurpose(Map<String, Object> m, ContractPaymentTerm cpt) {
        try {
            logger.info("cpt id: " + cpt.getId());

            ContractPaymentConfirmationToDo t = contractPaymentConfirmationToDoRepository
                    .findFirstByContractPaymentTerm_ContractAndSourceAndShowOnERPFalseAndDisabledFalseOrderByCreationDateDesc(
                            cpt.getContract(), Source.VISA_OVERSTAY_FEE);
            Double amount = (Double) m.get("amount");
            if (t != null) {
                if (t.getTotalAmount().equals(amount)) return t;

                contractPaymentConfirmationToDoRepository.delete(t);
            }

            ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
            toDo.setContractPaymentTerm(cpt);
            toDo.setSource(Source.VISA_OVERSTAY_FEE);
            toDo.setPaymentMethod(PaymentMethod.CARD);
            toDo.setPayingOnline(true);

            ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
            wrapper.setContractPaymentConfirmationToDo(toDo);
            wrapper.setPaymentDate(DateTimeFormat.forPattern("yyyy-MM-dd")
                    .parseLocalDate((String) m.get("paymentDate")).toDate());
            wrapper.setActualReceivedAmount(amount);
            wrapper.setAmount(amount);
            wrapper.setPaymentType(picklistItemRepository
                    .findOne(Long.valueOf((Integer) m.get("paymentTypeId"))));
            wrapper.setDescription("Overstay Fee");
            wrapper.setAffectedByAdditionalDiscount(false);
            // Overstay Fee not Including Worker Salary
            wrapper.setIncludeWorkerSalary(false);

            List<ContractPaymentWrapper> wrappers = new ArrayList<>();
            wrappers.add(wrapper);
            toDo.setContractPaymentList(wrappers);
            toDo.setDescription(wrapper.getDescription());
            toDo.setPaymentType(wrapper.getPaymentType());

            Setup.getApplicationContext()
                    .getBean(ContractPaymentConfirmationToDoService.class)
                    .createConfirmationToDo(toDo);
            return contractPaymentConfirmationToDoRepository.findOne(toDo.getId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public DateTime getUpcomingMonthlyPaymentDatePerTodo(ContractPaymentConfirmationToDo t) {
        ContractPaymentWrapper w = t.getContractPaymentList().stream()
                .filter(wr -> PaymentHelper.isMonthlyPayment(wr.getPaymentType()))
                .sorted(Comparator.comparing(ContractPaymentWrapper::getPaymentDate).reversed())
                .findFirst().orElse(null);

        return w == null ? null : new DateTime(w.getPaymentDate()).plusMonths(1).dayOfMonth().withMinimumValue();
    }

    public Map<String, Object> validateCreateToDoFromErp(ContractPaymentConfirmationToDo toDo) {
        Map<String, Object> m = new HashMap<>();
        m.put("valid", true);

        if (!toDo.isPayingOnline()) return m;

        ContractPaymentWrapper receivedPayment = toDo.getContractPaymentList()
                .stream()
                .filter(w -> paymentService.isPaymentReceivedByCountPaymentsAndRefunds(
                        toDo.getContractPaymentTerm().getContract(),
                        w.getAmount(),
                        picklistItemRepository.findOne(w.getPaymentType().getId()),
                        new LocalDate(w.getPaymentDate())))
                .findFirst()
                .orElse(null);

        if (receivedPayment != null) {
            m.put("message", "There is an existing received payment covering the same period with the amount AED " +
                    receivedPayment.getAmount().intValue() + " (Payment type is " +
                    picklistItemRepository.findOne(receivedPayment.getPaymentType().getId()).getName() +
                    ", Payment date is " + new LocalDate(receivedPayment.getPaymentDate()).toString("yyyy-MM-dd") +
                    "). Please check and correct");
            m.put("valid", false);
            return m;
        }

        List<ContractPaymentWrapper> monthlyPayment =  toDo.getContractPaymentList()
                .stream()
                .filter(w -> PaymentHelper.isMonthlyPayment(w.getPaymentType()))
                .collect(Collectors.toList());

        if (!monthlyPayment.isEmpty()) {
            List<ContractPayment> contractPayments = Setup.getApplicationContext()
                    .getBean(DirectDebitRejectionFlowService.class)
                    .getAllContractsPaymentRelatedWithDdbRejected(toDo.getContractPaymentTerm())
                    .stream()
                    .filter(cp -> monthlyPayment
                            .stream()
                            .anyMatch(w ->
                                    w.getAmount().equals(cp.getAmount()) &&
                                            new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd")
                                                    .equals(new LocalDate(cp.getDate()).toString("yyyy-MM-dd"))))
                    .collect(Collectors.toList());

            DirectDebit dd = contractPayments.stream()
                    .map(ContractPayment::getDirectDebit)
                    .filter(d -> d.getDirectDebitRejectionToDo() != null)
                    .max(Comparator.comparing(DirectDebit::getCreationDate))
                    .orElse(null);

            if (dd != null) {
                Map<String, Object> payMap = new HashMap<>();
                payMap.put("relatedEntityId", dd.getId());
                payMap.put("relatedEntityType", dd.getEntityType());
                payMap.put("relatedFlowId", dd.getDirectDebitRejectionToDo().getId());
                payMap.put("relatedFlowEntityType", dd.getDirectDebitRejectionToDo().getEntityType());

                m.put("message", "You cannot add or select a payment related to a DDB under an ongoing collection flow. " +
                        "Please use this link, which is the same one already being sent by the flow, " +
                        "and share it with the client so they can pay their due amount using a credit card.");
                m.put("url", AccountingLinkService.getDynamicPayTabsLink(payMap, toDo.getContractPaymentTerm()));
                m.put("source", "DirectDebitRejected");
                m.put("valid", false);
                return m;
            }
        }

        List<ContractPaymentConfirmationToDo> matchedToDos = getMatchedToDoViaWrappers(toDo);
        if (matchedToDos.isEmpty()) return m;

        for (ContractPaymentConfirmationToDo matchedToDo : matchedToDos) {
            m = validateCreateToDoFromErpViaToDo(toDo, matchedToDo, m);
            if (!((boolean) m.get("valid"))) return m;
        }

        return validateMonthlyPaymentWrappers(toDo, m);
    }

    // ACC-9507
    private Map<String, Object> validateMonthlyPaymentWrappers(ContractPaymentConfirmationToDo toDo, Map<String, Object> m) {

        List<ContractPaymentWrapper> monthlyWrappers = toDo.getContractPaymentList()
                .stream()
                .filter(wrapper -> PaymentHelper.isMonthlyPayment(wrapper.getPaymentType()))
                .collect(Collectors.toList());

        if (monthlyWrappers.isEmpty()) return m;

        for (ContractPaymentWrapper w : monthlyWrappers) {
            List<Payment> l = paymentRepository.findConflictMonthlyPaymentByContractAndDateOfPaymentBetweenAndStatus(
                    toDo.getContractPaymentTerm().getContract(),
                    new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate(),
                    new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate());

            if (!l.isEmpty()) {
                m.put("valid", false);
                m.put("message", "err1");
                m.put("paymentId", l.get(0).getId());
                m.put("typeOfPayment", l.get(0).getTypeOfPayment().getLabel());
                m.put("amount", l.get(0).getAmountOfPayment());
                m.put("paymentDate", l.get(0).getDateOfPayment());
                return m;
            }
        }

        return m;
    }

    public Map<String, Object> validateCreateToDoFromErpViaToDo(
            ContractPaymentConfirmationToDo toDo, ContractPaymentConfirmationToDo matchedToDo, Map<String, Object> m) {
        logger.info("Matched toTo id: " + matchedToDo.getId());

        boolean matchedTodoHasSamePayments = toDo.getTotalAmount().equals(matchedToDo.getTotalAmount());
        for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {
            boolean matched = false;

            for (ContractPaymentWrapper w1 : matchedToDo.getContractPaymentList()) {
                if (w.getReplacedBouncedPaymentId() != null &&
                        w.getReplacedBouncedPaymentId().equals(w1.getReplacedBouncedPaymentId())) {
                    matched = true;
                    logger.info("matched with w1 id: " + w1.getId() + " ReplacedBouncedPaymentId: " + w1.getReplacedBouncedPaymentId());
                } else if (w.getReplacedFuturePaymentId() != null &&
                        w.getReplacedFuturePaymentId().equals(w1.getReplacedFuturePaymentId())) {
                    matched = true;
                    logger.info("matched with w1 id: " + w1.getId() + " ReplacedFuturePaymentId: " + w1.getReplacedFuturePaymentId());
                } else {
                    matched = w.getAmount().equals(w1.getAmount()) &&
                            new LocalDate(w.getPaymentDate()).toString("yyyy-MM")
                                    .equals(new LocalDate(w1.getPaymentDate()).toString("yyyy-MM")) &&
                            w.getPaymentType().getId().equals(w1.getPaymentType().getId());
                }

                if (matched) {
                    logger.info("w amount: " + w.getAmount() +
                            "; date: " + new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd") +
                            "; type: " + w1.getPaymentType().getCode() +
                            ": matched with w1 id: " + w1.getId());
                    m.put("amount", w.getAmount());
                    break;
                }
            }

            if (!matched) {
                matchedTodoHasSamePayments = false;
            }

            if (!matchedTodoHasSamePayments && m.containsKey("amount")) break;
        }


        if (matchedTodoHasSamePayments && matchedToDo.getContractPaymentList().size() == toDo.getContractPaymentList().size()) {
            m.put("message", "There is an existing payment covering the same period and the same amount created already. " +
                    "Please share the existing link again with the client instead of creating a new one. You can find the link by clicking on <Copy URL> button below.");
            m.put("url", getPayingViaCreditCardLink(matchedToDo));
            m.put("todoUuid", matchedToDo.getUuid());
            m.put("todoId", matchedToDo.getId());
            List<FlowProcessorEntity> l = flowProcessorEntityRepository.findByContractPaymentConfirmationToDoOrderByRunning(matchedToDo);
            m.put("hideTriggerReminderFlow", l.isEmpty() || Source.BOUNCED_PAYMENT_FLOW.equals(matchedToDo.getSource()));
            m.put("source", !l.isEmpty() ? l.get(0).getFlowEventConfig().getName() : matchedToDo.getSource().getLabel());
            m.put("valid", false);
        } else if (matchedToDo.getSource().equals(Source.ERP)) {
            m.put("message", "The following payment AED " + ((Double) m.get("amount")).intValue() + " is already included in a previous link. " +
                    "Please either delete the previous payment and then you can include it in this new link or " +
                    "share the old one with the client to collect this payment.");
            m.put("valid", false);
        }

        m.remove("amount");
        return m;
    }

    public List<ContractPaymentConfirmationToDo> getMatchedToDoViaWrappers(ContractPaymentConfirmationToDo toDo) {

        StringBuilder baseSelect = new StringBuilder("select distinct t from ContractPaymentWrapper w " +
                "join w.contractPaymentConfirmationToDo t " +
                "where ");

        Map<String, Object> para = new HashMap<>();
        if (toDo.getId() != null) {
            baseSelect.append( " t.id <> :todoId and ");
            para.put("todoId", toDo.getId());
        }

        baseSelect.append( " t.contractPaymentTerm.contract.id = :contractId and t.payingOnline = true and " +
                "t.showOnERP = false and t.disabled = false and t.creditCardOffer = false ");
        for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {

            para.put("contractId", toDo.getContractPaymentTerm().getContract().getId());

            String select = " and w.amount = :amount " +
                    "and w.paymentType.id = :paymentTypeId " +
                    "and w.paymentDate >= :startDate " +
                    "and w.paymentDate <= :endDate ";
            para.put("amount", w.getAmount());
            para.put("paymentTypeId", w.getPaymentType().getId());
            para.put("startDate", new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate());
            para.put("endDate", new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());

            List<ContractPaymentConfirmationToDo> result = new SelectQuery<>(
                    baseSelect + select, "", ContractPaymentConfirmationToDo.class, para)
                    .execute();

            if (!result.isEmpty()) return result;
        }

        return new ArrayList<>();
    }

    public boolean invalidToDo(ContractPaymentConfirmationToDo t) {

        return t.getContractPaymentList()
                .stream()
                .anyMatch(w ->
                        paymentService.isPaymentReceivedByCountPaymentsAndRefunds(
                                t.getContractPaymentTerm().getContract(),
                                w.getAmount(),
                                w.getPaymentType(),
                                new LocalDate(w.getPaymentDate())));
    }

    @Transactional
    public ContractPaymentConfirmationToDo createConfirmationToDoForRefund(ClientRefundToDo clientRefundToDo) throws Exception {
        logger.info("clientRefundToDo id: " + clientRefundToDo.getId() +
                "; Related Payment id: " + clientRefundToDo.getRelatedPaymentId());

        ContractPaymentTerm cpt = clientRefundToDo.getContract().getActiveContractPaymentTerm();
        Payment p = clientRefundToDo.getRelatedPaymentId() == null ?
                null : paymentRepository.findOne(clientRefundToDo.getRelatedPaymentId());
        if (p == null || clientRefundToDo.getPurpose() == null ||
                clientRefundToDo.getPurpose().getTypeOfPayment() == null) return null;

        ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
        toDo.setContractPaymentTerm(cpt);
        toDo.setSource(Source.CLIENT_REFUND);
        toDo.setPaymentType(clientRefundToDo.getPurpose().getTypeOfPayment());
        toDo.setPaymentMethod(PaymentMethod.CARD);
        toDo.setPayingOnline(true);
        toDo.setTransferReference(clientRefundToDo.getTransferReference());
        toDo.setDescription(clientRefundToDo.getPurpose().getTypeOfPayment().getName());
        toDo.setAttachments(p.getAttachments());

        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setContractPaymentConfirmationToDo(toDo);
        wrapper.setPaymentDate(p.getDateOfPayment());
        wrapper.setProrated(false);
        wrapper.setInitial(false);
        wrapper.setAmount(clientRefundToDo.getAmount());
        wrapper.setVatPaidByClient(p.getVat() != null && !p.getVat().equals(0.0));
        wrapper.setIncludeWorkerSalary(p.getIncludeWorkerSalary());
        // TODO issue in another task: It's todo level, not the level of payment.
        wrapper.setWorkerSalary(p.getWorkerSalary());
        wrapper.setDiscountAmount(p.getDiscount());

        wrapper.setDescription(toDo.getDescription());
        wrapper.setPaymentType(clientRefundToDo.getPurpose().getTypeOfPayment());

        toDo.getContractPaymentList().add(wrapper);

        return createConfirmationToDo(toDo);
    }

    public void linkRefundPaymentWithConfirmationToDo(ClientRefundToDo c) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "linkRefundPaymentWithConfirmationToDO_" + c.getId(),
                        "accounting",
                        "contractPaymentConfirmationToDoService",
                        "linkRefundPaymentWithConfirmationToDo")
                        .withRelatedEntity("ClientRefundToDo", c.getId())
                        .withParameters(new Class[]{ Long.class }, new Object[]{ c.getId() })
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(60 * 1000L)
                        .withRetryDelay(3 * 60 * 1000L)
                        .build());
    }

    public void linkRefundPaymentWithConfirmationToDo(Long refundId) {
        logger.info("refundId: " + refundId);
        ClientRefundToDo clientRefundToDo = Setup.getRepository(ClientRefundTodoRepository.class).findOne(refundId);
        Payment p = paymentRepository.findFirstByClientRefundToDo_IdOrderByDateOfPaymentAsc(refundId);
        logger.info("Payment id: " + p.getId());

        ContractPaymentWrapper w = clientRefundToDo.getContractPaymentConfirmationToDo()
                .getContractPaymentList().stream()
                .filter(wrapper -> wrapper.getAmount().equals(p.getAmountOfPayment()) &&
                        wrapper.getPaymentType().getCode().equals(p.getTypeOfPayment().getCode()))
                .findFirst()
                .orElse(null);

        if (w == null) return;
        logger.info("wrapper id: " + w.getId());
        w.setGeneratedPaymentId(p.getId());
        w.setPaymentDate(p.getDateOfPayment());
        Setup.getRepository(ContractPaymentWrapperRepository.class).save(w);

        w.getContractPaymentConfirmationToDo().setShowOnERP(true);
        contractPaymentConfirmationToDoRepository.save(w.getContractPaymentConfirmationToDo());
    }

    // ACC-7156
    public List<Map<Object, Object>> getOnlineCreditCardLinkViaPayment(long[] ids) {
        if (ids == null || ids.length == 0) return new ArrayList<>();

        List<Map<Object, Object>> results = new ArrayList<>();

        contractPaymentConfirmationToDoRepository.findConfirmationToDoViaPayment(ids)
                .forEach(result -> {
                    Map<Object, Object> r = new HashMap<>();
                    Long paymentId = (Long) result.get("paymentId");
                    ContractPaymentConfirmationToDo todo = (ContractPaymentConfirmationToDo) result.get("todo");

                    if (isTodoExpired(todo)) {
                        return;
                    }
                    r.put("paymentId", paymentId);
                    r.put("paymentLink", getPayingViaCreditCardLink(todo));
                    List<Map<String, Object>> paymentMaps = todo.getContractPaymentList().stream()
                            .map(w -> new HashMap<String, Object>() {{
                                put("paymentDate", w.getPaymentDate());
                                put("amount", w.getAmount().intValue());
                                put("type", w.getPaymentType().getName());
                    }})
                    .collect(Collectors.toList());
                    r.put("payments", paymentMaps);
                    results.add(r);
        });

        return results;
    }

    public void deleteDisabledToDoExistingPayment(ContractPaymentConfirmationToDo toDo) {

        Setup.getApplicationContext()
                .getBean(DisableAccountingNotificationService.class)
                .disableOnClientPaidOnlinePaymentViaCard(toDo.getId());

        if (toDo.getContractPaymentList().isEmpty()) return;

        toDo.getContractPaymentList().stream()
                .filter(w -> w.getGeneratedPaymentId() != null)
                .forEach(w -> {
                    Payment p = paymentRepository.findOne(w.getGeneratedPaymentId());
                    if (p != null && p.getStatus().equals(PaymentStatus.PRE_PDP)) {
                        logger.info("payment id: " + p.getId());
                        p.setStatus(PaymentStatus.DELETED);
                        p.setPassStopConfirmationTodo(true);
                        paymentService.forceUpdatePayment(p);
                    }
                });
    }

    // ACC-8513
    @UsedBy(modules = UsedBy.Modules.Visa)
    @Transactional
    public void waiveOverStayFeePayment(Map<String, Object> body) throws Exception {
        logger.info("body: " +  new ObjectMapper().writeValueAsString(body));

        ContractPaymentConfirmationToDo t = contractPaymentConfirmationToDoRepository
                .findFirstByRelatedEntityIdAndRelatedEntityTypeAndDisabledFalseAndShowOnERPFalse(Long.valueOf(body.get("overstayFinesId").toString()), "OverstayFines");
        if (t == null) return;

        t.setDisabled(true);
        contractPaymentConfirmationToDoRepository.save(t);
        FlowProcessorEntity f = flowProcessorEntityRepository.findFirstByContractPaymentConfirmationToDoAndStoppedFalseAndCompletedFalse(t);
        if (f != null) {
            f.setStopped(true);
            f.setCausedTermination(false);
            f.setStoppedDueContractTerminated(false);
            flowProcessorEntityRepository.save(f);
        }

        if ((boolean) body.get("fullWaive")) {
            return;
        }

        List<ContractPayment> l = Collections.singletonList(Setup.getApplicationContext()
                .getBean(ContractPaymentService.class)
                .createContractPaymentForDda(
                        t.getContractPaymentTerm().getContract().getActiveContractPaymentTerm(),
                        t.getContractPaymentList().get(0).getPaymentDate(),
                        (Double) body.get("newAmount"),
                        t.getContractPaymentList().get(0).getPaymentType()));

        Map<String, Object> m = new HashMap<>();
        m.put("relatedToEntityId", t.getRelatedEntityId());
        m.put("relatedToEntityType", t.getRelatedEntityType());
        ContractPaymentConfirmationToDo newTodo = createConfirmationTodoFromContractPayments(
                t.getContractPaymentTerm(),
                l,
                ContractPaymentConfirmationToDo.Source.PAYMENT_REMINDER,
                m);

        unpaidOnlineCreditCardPaymentService.createPaymentReminderFlow(newTodo, newTodo.getContractPaymentTerm());
    }

    // ACC-8513
    private void markOverstayFinesAsPaid(ContractPaymentConfirmationToDo t) {

        Map<String, Object> m = new HashMap<>();
        m.put("id", t.getRelatedEntityId());
        m.put("overstayFinesStatus", "Paid");
        m.put("datePaid", new LocalDate().toString("yyyy-MM-dd"));
        logger.info(m.entrySet().toString());
        moduleConnector.postJsonAsync("/visa/overstay-fines/update", m);
    }

    public ContractPaymentConfirmationToDo createToDoForRecurringPaymentIfNotExists(Payment payment, ContractPaymentTerm cpt) {
        logger.info("cpt id: " + cpt.getId());
        List<ContractPaymentConfirmationToDo> toDos = contractPaymentConfirmationToDoRepository.findToDoForRecurringCaptureByPaymentId(
                payment.getId(), clientPayingViaCreditCardService.getPayingViaCcSource(cpt.getContract()));
        if (!toDos.isEmpty()) {
            return toDos.get(0);
        }

        ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
        toDo.setContractPaymentTerm(cpt);
        toDo.setSource(clientPayingViaCreditCardService.getPayingViaCcSource(cpt.getContract()));
        toDo.setPaymentMethod(PaymentMethod.CARD);
        toDo.setPayingOnline(true);

        List<ContractPaymentWrapper> wrappers = new ArrayList<>();
        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setContractPaymentConfirmationToDo(toDo);
        wrapper.setPaymentDate( payment.getDateOfPayment());
        wrapper.setActualReceivedAmount( payment.getAmountOfPayment());
        wrapper.setAmount(payment.getAmountOfPayment());
        wrapper.setDiscountAmount(payment.getDiscount());
        wrapper.setPaymentType(payment.getTypeOfPayment());
        wrapper.setGeneratedPaymentId(payment.getId());
        wrapper.setInitial(payment.getIsInitial());
        wrapper.setIncludeWorkerSalary(payment.getIncludeWorkerSalary());
        // ACC-8422
        wrapper.setWorkerSalary(payment.getWorkerSalary());

        ContractPaymentType type = cpt.getContractPaymentTypes()
                .stream()
                .filter(t -> t.getType().getCode().equals(wrapper.getPaymentType().getCode()))
                .findFirst()
                .orElse(null);
        wrapper.setDescription(type != null ? type.getDescription() : wrapper.getPaymentType().getName());
        wrapper.setAffectedByAdditionalDiscount( false);

        if (payment.getSubType() != null) {
            wrapper.setSubType(payment.getSubType());
        }

        wrappers.add(wrapper);

        toDo.setContractPaymentList(wrappers);
        toDo.setDescription(wrappers.get(0).getDescription());
        toDo.setPaymentType(wrappers.get(0).getPaymentType());

        Setup.getApplicationContext()
                .getBean(ContractPaymentConfirmationToDoService.class)
                .createConfirmationToDo(toDo);
        toDo = contractPaymentConfirmationToDoRepository.findOne(toDo.getId());

        if (toDo != null && !toDo.getContractPaymentList().isEmpty()) {
            ContractPayment cp = toDo.getContractPaymentList().get(0).getContractPayment();
            if (cp != null && cp.getId() != null) {
                logger.info("link payment id: " + payment.getId() + " with cp: "  + cp.getId());
                payment.setContractPaymentId(cp.getId());
                paymentService.updatePaymentSilent(payment);
            }
        }

        return toDo;
    }

    public ContractPaymentConfirmationToDo generateConfirmationToDoForAuthorization(ContractPaymentTerm cpt, ContractPaymentConfirmationToDo.Purpose purpose) {

        logger.info("cpt id: " + cpt.getId());

        ContractPaymentConfirmationToDo toDo = new ContractPaymentConfirmationToDo();
        toDo.setContractPaymentTerm(cpt);
        toDo.setSource(Source.AUTHORIZATION);
        toDo.setPaymentMethod(PaymentMethod.CARD);
        toDo.setPayingOnline(true);
        toDo.setShowOnERP(false);
        toDo.setPurpose(purpose);

        List<ContractPaymentWrapper> wrappers = new ArrayList<>();
        ContractPaymentWrapper wrapper = new ContractPaymentWrapper();
        wrapper.setContractPaymentConfirmationToDo(toDo);
        wrapper.setPaymentDate(new Date());
        wrapper.setActualReceivedAmount(1.0D);
        wrapper.setAmount(1.0D);
        wrapper.setInitial(false);
        wrapper.setPaymentType(PicklistHelper.getItem(
                AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE));
        ContractPaymentType type = cpt.getContractPaymentTypes()
                .stream()
                .filter(t -> t.getType().getCode().equals(wrapper.getPaymentType().getCode()))
                .findFirst()
                .orElse(null);
        wrapper.setDescription(type != null ? type.getDescription() : wrapper.getPaymentType().getName());
        wrapper.setAffectedByAdditionalDiscount(false);
        // Authorization Recurring Payment not Including Worker Salary
        wrapper.setIncludeWorkerSalary(false);
        wrappers.add(wrapper);

        toDo.setContractPaymentList(wrappers);
        toDo.setDescription(wrappers.get(0).getDescription());
        toDo.setPaymentType(wrappers.get(0).getPaymentType());

        return contractPaymentConfirmationToDoRepository.save(toDo);
    }

    public static boolean isEligibleForTokenizationViaConfirmationToDO(ContractPaymentConfirmationToDo toDo) {
        if (!toDo.getContractPaymentTerm().isActive() ||
                !ContractService.isEligibleForTokenizationViaContract(toDo.getContractPaymentTerm().getContract())) {
            return false;
        }

        // Check if exists Running EXPIRED_CARD flow and should be asked the client save the new card info
        if (toDo.getContractPaymentTerm().getSourceId() != null &&
                !Setup.getApplicationContext()
                        .getBean(FlowProcessorService.class)
                        .existsRunningFlow(
                                toDo.getContractPaymentTerm().getContract(),
                                FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                                FlowSubEventConfig.FlowSubEventName.EXPIRED_CARD)) {
            return false;
        }

        double monthlyPaymentAmount = toDo.getContractPaymentTerm().getContract().isOneMonthAgreement() ?
                toDo.getContractPaymentTerm().getMonthlyPayment() :
                toDo.getContractPaymentTerm().getDiscountedMonthlyPayment();

        boolean isEligible = toDo.getContractPaymentList().size() == 1 &&
                                toDo.getContractPaymentList().stream()
                                        .allMatch(w -> PaymentHelper.isMonthlyPayment(w.getPaymentType()) &&
                                                !w.getAffectedByAdditionalDiscount() && !w.isInitial() &&
                                                !w.isProrated() && w.getAmount().equals(monthlyPaymentAmount) &&
                                                (w.getMoreAdditionalDiscount() == null || w.getMoreAdditionalDiscount() == 0.0) &&
                                                DiscountsWithVatHelper.getAddOnPaymentType(
                                                                w.getPaymentDate(),
                                                                toDo.getContractPaymentTerm().getContractPaymentTypes(),
                                                        toDo.getContractPaymentTerm().getContract()) == null);
        logger.info("isEligible: " + isEligible);
        return isEligible;
    }

    public void disableAllContractPaymentConfirmationToDoByCptAndCreditCardOffer(ContractPaymentTerm cpt) {

        List<ContractPaymentConfirmationToDo> l = contractPaymentConfirmationToDoRepository
                .findAllConfirmationToDoByContractPaymentTermAndCreditCardOffer(cpt);
        if(l.isEmpty()) return;

        for(ContractPaymentConfirmationToDo todo : l){
            todo.setDisabled(true);
        }
        contractPaymentConfirmationToDoRepository.saveAll(l);
    }

    public boolean isWaitingRecurringToken(ContractPaymentTerm cpt) {
        List<ContractPaymentConfirmationToDo> l = contractPaymentConfirmationToDoRepository.findToDoWaitingRecurringToken(
                cpt, Arrays.asList(Source.AUTHORIZATION, clientPayingViaCreditCardService.getPayingViaCcSource(cpt.getContract())));
        if (l.isEmpty()) return false;

        for (ContractPaymentConfirmationToDo toDo : l) {
            logger.info("todo id: " + toDo.getId());
            SelectQuery<BackgroundTask> bgtQuery = new SelectQuery<>(BackgroundTask.class);
            bgtQuery.filterBy("relatedEntityId", "=", toDo.getId());
            bgtQuery.filterBy("relatedEntityType", "=", "ContractPaymentConfirmationToDo");
            bgtQuery.filterBy("name", "=", "paidViaPayTabs_" + toDo.getId());
            bgtQuery.filterBy("status", "NOT IN", Arrays.asList(BackgroundTaskStatus.Finished, BackgroundTaskStatus.Failed));
            if (!bgtQuery.execute().isEmpty()) return true;

            SelectQuery<EPaymentTransaction> tQuery = new SelectQuery<>(EPaymentTransaction.class);
            tQuery.filterBy("localReference", "=", AccountingEPaymentService.getToDoIdentifier(toDo));
            tQuery.filterBy(new SelectFilter("isApplied", "=", false))
                    .and(new SelectFilter("providerPageLinkId", "is not null", null)
                            .or("transReference", "is not null", null))
                    .and(new SelectFilter("transactionStatus", "is null", null)
                            .or(new SelectFilter("transactionStatus", "in",
                                    Arrays.asList(ETransactionStatus.PENDING,
                                            ETransactionStatus.CREATED,
                                            ETransactionStatus.SUCCESS,
                                            ETransactionStatus.AUTHORIZED))));

            tQuery.filterBy("transactionType", "=",
                    toDo.getPurpose().equals(ContractPaymentConfirmationToDo.Purpose.CC_APP_ADD_NEW_CARD) ?
                            EpaymentTransactionType.AUTHORIZATION :
                            EpaymentTransactionType.RECURRING_CLIENT_INITIATED);
            tQuery.sortBy("creationDate", false, true);
            tQuery.setLimit(1);
            List<EPaymentTransaction> tList = tQuery.execute();
            if (!tList.isEmpty() &&
                    (tList.get(0).getCustomerName() == null ||
                            tList.get(0).getCustomerName().isEmpty() ||
                            tList.get(0).getSourceId() == null ||
                            tList.get(0).getSourceId().isEmpty())) {
                logger.info("EPaymentTransaction id: " + tList.get(0).getId());
                return true;
            }
        }

        return false;
    }

    public Map<String, Object> checkMatchedToDoIfRelatedToRunningFlow(Long toDoId) {

        Map<String, Object> m = new HashMap<>();
        ContractPaymentConfirmationToDo toDo = contractPaymentConfirmationToDoRepository.findOne(toDoId);
        List<FlowProcessorEntity> l = flowProcessorEntityRepository.findRunningFlowByToDo(toDo.getId());
        if (l.isEmpty()) {
            m.put("hasActiveRunningFlow", false);
            toDo.setDisabled(true);
            contractPaymentConfirmationToDoRepository.silentSave(toDo);
        } else {
            m.put("hasActiveRunningFlow", true);
            m.put("flowId", l.get(0).getId());
            m.put("flowName", l.get(0).getFlowEventConfig().getName());
            m.put("required", toDo.isRequired() || !toDo.getSource().equals(Source.ERP));
        }
        return  m;
    }

    @Transactional
    public ContractPaymentConfirmationToDo createToDoERPSource(ContractPaymentConfirmationToDo toDo) {
        toDo.setSource(Source.ERP);
        // ACC-8407
        if (((toDo.isPayingOnline() && toDo.getPaymentMethod().equals(PaymentMethod.CARD)) ||
                toDo.getPaymentMethod().equals(PaymentMethod.WIRE_TRANSFER)) &&
                toDo.getContractPaymentList().stream()
                        .anyMatch(e -> e.isCreationOfNewPayment() && e.getAmount() == 0.0)) {
            throw new BusinessException("Error: You can't add a waived payment " +
                    "with payment method online card or wire transfer");
        }

        // ACC-8407
        if (((toDo.isPayingOnline() && toDo.getPaymentMethod().equals(PaymentMethod.CARD)) ||
                toDo.getPaymentMethod().equals(PaymentMethod.WIRE_TRANSFER)) &&
                toDo.getContractPaymentList().stream()
                        .anyMatch(e -> e.isCreationOfNewPayment() && e.getAmount() == 0.0)) {
            throw new BusinessException("Error: You can't add a waived payment " +
                    "with payment method online card or wire transfer");
        }

        // ACC-8662
        // Check and Start Prevent Create Other Dds
        Setup.getApplicationContext()
                .getBean(ContractService.class)
                .validateAndStartPreventCreateOtherDds(toDo.getContractPaymentTerm());

        if (toDo.getContractPaymentList().stream().anyMatch(w -> w.getAmount().equals(0.0D))) {
            for (ContractPaymentWrapper w : toDo.getContractPaymentList()) {
                if (!w.getAmount().equals(0.0D)) continue;
                PicklistItem paymentType = picklistItemRepository.findOne(w.getPaymentType().getId());
                ContractPaymentType contractPaymentType = toDo.getContractPaymentTerm()
                        .getContractPaymentTypes().stream()
                        .filter(c -> c.getType().getCode().equals(paymentType.getCode()))
                        .findFirst().orElse(null);
                paymentService.addWaivedPayment(toDo.getContractPaymentTerm(), w.getPaymentDate(),
                        paymentService.getWaivedPayment(toDo.getContractPaymentTerm().getContract(), paymentType, w.isProrated()),
                        paymentType,
                        contractPaymentType != null ? contractPaymentType.getDescription() : paymentType.getName(),
                        new HashMap<String, Object>() {{
                            put("isProRated", w.isProrated());
                        }});
            }
        }

        toDo.setContractPaymentList(toDo.getContractPaymentList()
                .stream().filter(w -> w.getAmount() > 0).collect(Collectors.toList()));

        if (toDo.getContractPaymentList().isEmpty()) return null;

        return createConfirmationToDo(toDo);
    }

    @Transactional
    public void deleteTokenizedPdpPayments(ContractPaymentConfirmationToDo t) {
        if(!t.getSource().equals(Source.ERP)) return;

        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(t.getContractPaymentTerm().getId());
        PicklistItem monthlyType = Setup.getItem("TypeOfPayment", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        for (ContractPaymentWrapper w : t.getContractPaymentList()) {
            if (!monthlyType.getId().equals(w.getPaymentType().getId())) {
                continue;
            }

            List<Payment> payments = paymentRepository.
                    findByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndDateOfPaymentAndRecurring(
                            cpt.getContract(),
                            monthlyType,
                            new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate(),
                            new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());

            payments.forEach(p -> {
                p.setStatus(PaymentStatus.DELETED);
                logger.info("payment deleted id: " + p.getId());
                paymentService.forceUpdatePayment(p);
            });
        }
    }


    public Map<String, Object> checkForExistingTokenizedPayments(ContractPaymentConfirmationToDo todo) {
        List<Map<String, Object>> matches = new ArrayList<>();
        ContractPaymentTerm cpt = contractPaymentTermRepository.findOne(todo.getContractPaymentTerm().getId());
        PicklistItem monthlyType = Setup.getItem("TypeOfPayment", AbstractPaymentTypeConfig.MONTHLY_PAYMENT_TYPE_CODE);

        for (ContractPaymentWrapper w : todo.getContractPaymentList()) {
            if (w.getPaymentType() == null || !monthlyType.getId().equals(w.getPaymentType().getId())) {
                continue;
            }

            boolean tokenizedPaymentsExist = paymentRepository.
                    existsByContractAndMethodOfPaymentAndStatusAndTypeOfPaymentAndDateOfPaymentAndRecurring(
                            cpt.getContract(),
                            monthlyType,
                            new LocalDate(w.getPaymentDate()).dayOfMonth().withMinimumValue().toDate(),
                            new LocalDate(w.getPaymentDate()).dayOfMonth().withMaximumValue().toDate());

            if (tokenizedPaymentsExist) {
                Map<String, Object> match = new HashMap<>();
                match.put("date", new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd"));
                matches.add(match);
            }
        }

        return new HashMap<String, Object>() {{
            put("hasMatches", !matches.isEmpty());
            put("matches", matches);
        }};
    }

    public List<ContractPaymentConfirmationToDo> getMatchedToDoViaWrappers(
            List<ContractPaymentConfirmationToDo> toDos,
            List<Map<String, Object>> paymentsInfo) {

        List<ContractPaymentConfirmationToDo> toDosMatched = toDos.stream()
                .filter(t -> t.getContractPaymentList().stream()
                        .allMatch(w -> paymentsInfo.stream()
                                .anyMatch(cp ->
                                        w.getAmount().equals(cp.get("amount")) &&
                                                w.getPaymentType().getCode().equals(cp.get("typeCode")) &&
                                                new LocalDate(w.getPaymentDate()).toString("yyyy-MM-dd")
                                                        .equals(new LocalDate(cp.get("date")).toString("yyyy-MM-dd")))))
                .collect(Collectors.toList());

        logger.info("ToDos Matched: " + toDos.size());
        return toDosMatched;
    }
}