package com.magnamedia.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.entity.*;
import com.magnamedia.entity.dto.ClientMgmtPaymentDetailsDto;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.extra.DiscountsWithVatHelper;
import com.magnamedia.extra.PaymentReceiptHelper;
import com.magnamedia.extra.StringUtils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.workflow.type.ClientRefundStatus;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import static com.magnamedia.entity.workflow.FlowSubEventConfig.FlowSubEventName.*;

/**
 * <AUTHOR> Karra
 * MC-28
 */
@Service
public class ContractSummaryService {
    private final static Logger logger = Logger.getLogger(ContractSummaryService.class.getName());

    @Autowired
    private PaymentService paymentService;
    @Autowired
    private PaymentRepository paymentRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private FlowProcessorService flowProcessorService;
    @Autowired
    private InterModuleConnector interModuleConnector;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private AccountingLinkRepository accountingLinkRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private ClientRefundTodoRepository clientRefundTodoRepository;
    @Autowired
    private ContractPaymentTypeRepository contractPaymentTypeRepository;
    @Autowired
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    @Autowired
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    @Autowired
    private DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    @Autowired
    private ContractPaymentConfirmationToDoService contractPaymentConfirmationToDoService;
    @Autowired
    private ContractPaymentConfirmationToDoRepository contractPaymentConfirmationToDoRepository;

    public static String SDR_PAYMENT_TYPE = "same_day_recruitment_fee";
    public static String INSURANCE_PAYMENT_TYPE = "insurance";

    /**
     * used in API in ContractController
     * @param contractId Long
     */
    public Map<String, Object> getPaymentDetailsForMVClients(Long contractId) {

        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) throw new BusinessException("Contract Not Found");

        ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
        logger.info("cpt id : " + cpt.getId() + ", contract id : " + contract.getId());

        Map<String, Object> r = new HashMap<>();

        // 1. First Section
        r.put("paymentPlan", PaymentReceiptHelper.getPaymentsPlanAsInDdcPage(cpt));

        // 2. Second Section
        List<ClientMgmtPaymentDetailsDto> l = fillPaymentDetailsTableSecondSection(contract, cpt);
        shiftingRecurringAndFutureNotRecurringPayments(l);
        r.put("paymentDetailsTable", l);

        // 3. Third Section
        r.put("generalPaymentInfo", fillGeneralPaymentInfoThirdSection(contract, cpt, l));

        return r;
    }

    private void shiftingRecurringAndFutureNotRecurringPayments(List<ClientMgmtPaymentDetailsDto> l) {
        if (l.isEmpty()) return;

        Contract contract = l.get(0).getContract();

        // 1. sort the payments
        l.sort(Comparator.comparing(ClientMgmtPaymentDetailsDto::getDueDate));

        // 2. create sublist for recurring and not recurring payments
        List<ClientMgmtPaymentDetailsDto> subList = l.stream()
                .filter(dto -> dto.getType().equals(ContractSummaryPaymentType.MONTHLY_RECURRING) ||
                        dto.getType().equals(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING))
                .collect(Collectors.toList());

        // 3. remove the elements from the origin list
        l.removeIf(dto -> dto.getType().equals(ContractSummaryPaymentType.MONTHLY_RECURRING) ||
                dto.getType().equals(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING));

        // 4. apply shifting dates and remove payments if required from sublist
        for(int i = 0; i < subList.size(); i++) {
            ClientMgmtPaymentDetailsDto current = subList.get(i);
            LocalDate outerCurrentDate = current.getDueDateLocal();
            if (paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                    contract, PaymentStatus.RECEIVED, "monthly_payment",
                    outerCurrentDate.dayOfMonth().withMinimumValue().toDate(),
                    outerCurrentDate.dayOfMonth().withMaximumValue().toDate())) {

                if (i == subList.size() - 1) {
                    current.setDueDate(outerCurrentDate.plusMonths(1).toDate());
                } else {
                    ClientMgmtPaymentDetailsDto next = subList.get(i+1);
                    if (next.getDueDateLocal().toString("yyyy-MM").equals(outerCurrentDate.plusMonths(1).toString("yyyy-MM"))) {
                        subList.remove(i);
                        i--;
                    } else {
                        current.setDueDate(outerCurrentDate.plusMonths(1).toDate());
                        int j = i+1;
                        do {
                            next = subList.get(j);
                            if (paymentRepository.existsByContractAndStatusAndTypeOfPayment_CodeAndDateOfPaymentBetween(
                                    contract, PaymentStatus.RECEIVED, "monthly_payment",
                                    next.getDueDateLocal().dayOfMonth().withMinimumValue().toDate(),
                                    next.getDueDateLocal().dayOfMonth().withMaximumValue().toDate())) {
                                subList.remove(j);
                                i--;
                                break;
                            } else {
                                next.setDueDate(outerCurrentDate.plusMonths(1).toDate());
                                j++;
                            }
                        } while(j <= subList.size() - 1);
                    }
                }
            }
        }

        // 5. add the sublist if not empty into origin list
        if (!subList.isEmpty()) {
            l.addAll(subList);
        }
    }

    /**
     * Second Section
     */
    private List<ClientMgmtPaymentDetailsDto> fillPaymentDetailsTableSecondSection(Contract contract, ContractPaymentTerm cpt) {
        List<ClientMgmtPaymentDetailsDto> result = new ArrayList<>();

        Map<ContractSummaryPaymentType, Set<String>> missingPayments = new HashMap<>();

        // 1. MONTHLY_PREVIOUS_MONTH
        Date startDate = new LocalDate().minusMonths(1).dayOfMonth().withMinimumValue().toDate();
        Date endDate = new LocalDate().minusMonths(1).dayOfMonth().withMaximumValue().toDate();
        result.add(getPaymentDetails(contract, "monthly_payment", startDate, endDate));

        // 2. MONTHLY_CURRENT_MONTH
        startDate = new LocalDate().dayOfMonth().withMinimumValue().toDate();
        endDate = new LocalDate().dayOfMonth().withMaximumValue().toDate();
        ClientMgmtPaymentDetailsDto currentMonth = getPaymentDetails(contract, "monthly_payment", startDate, endDate);
        if(currentMonth != null) {
            result.add(currentMonth);
        } else {
            missingPayments.put(ContractSummaryPaymentType.MONTHLY_CURRENT_MONTH, null);
        }

        // 3. FUTURE_MONTHLY_NOT_RECURRING
        // 4. MONTHLY_RECURRING
        List<ClientMgmtPaymentDetailsDto> futureMonthly = getMonthlyFuturePayments(contract);
        if(!futureMonthly.isEmpty()) result.addAll(futureMonthly);
        else {
            missingPayments.put(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING, null);
            missingPayments.put(ContractSummaryPaymentType.MONTHLY_RECURRING, null);
        }

        if (contract.isMaidVisa()) {
            // 5. SDR_INSTALLMENT
            // 6. SDR_RENEWAL
            // 7. UPCOMING_SDR_RENEWAL
            // 8. SDR
            List<ClientMgmtPaymentDetailsDto> addedSdr = getRecurringNonMonthlyPayments(contract, SDR_PAYMENT_TYPE, missingPayments);
            result.addAll(addedSdr);

            // 9. INSURANCE
            // 10. UPCOMING_INSURANCE_RENEWAL
            List<ClientMgmtPaymentDetailsDto> addedInsurance = getRecurringNonMonthlyPayments(contract, INSURANCE_PAYMENT_TYPE, missingPayments);
            result.addAll(addedInsurance);
        }

        // 11. Non-Monthly Payments
        Set<String> s = new HashSet<>();
        result.addAll(getNonMonthlyPayments(contract, s));
        missingPayments.put(ContractSummaryPaymentType.NON_MONTHLY, null);

        // Check for Missing Types To handle it Using Contract Payment Term
        if (!missingPayments.isEmpty()) {
            List<ClientMgmtPaymentDetailsDto> l = getPaymentDetailsFromContractPaymentTerm(
                    cpt, missingPayments, result, s);
            result.addAll(l);
        }

        return result;
    }

    /**
     * Helper Functions for Second Section
     */
    private ClientMgmtPaymentDetailsDto getPaymentDetails(Contract c, String paymentTypeCode, Date startDate, Date endDate) {
        ClientMgmtPaymentDetailsDto r = new ClientMgmtPaymentDetailsDto(c);

        // Try to fetch the payment based on the parameters
        Payment payment = fetchPayment(c, paymentTypeCode, null, startDate, endDate);

        if (payment != null) {
            return generateRowFromPayment(payment);
        }

        logger.info("Try to fill the Payments from DirectDebit");
        ContractPayment contractPayment = fetchDDContractPayment(c, paymentTypeCode, startDate, endDate);
        if (contractPayment != null) {
            return generateRowFromContractPayment(contractPayment);
        }

        if (paymentTypeCode.equals("monthly_payment") && startDate != null &&
                new LocalDate(startDate).isBefore(new LocalDate().dayOfMonth().withMinimumValue())) {

            // All fields should be empty and set only status and paymentType
            logger.info("empty fields for previous monthly payment");
            r.setPaymentStatus(ContractSummaryPaymentStatus.NEW_CONTRACT_PAYMENT_DOESNT_EXIST.getLabel());
            r.setType(ContractSummaryPaymentType.MONTHLY_PREVIOUS_MONTH);
            return r;
        }

        logger.info("No payment nor DD were found");
        return null;
    }

    // Get Payments from CPT
    private List<ClientMgmtPaymentDetailsDto> getPaymentDetailsFromContractPaymentTerm(
            ContractPaymentTerm cpt,
            Map<ContractSummaryPaymentType, Set<String>> missingTypes,
            List<ClientMgmtPaymentDetailsDto> l,
            Set<String> s) {

        List<ClientMgmtPaymentDetailsDto> rowsList = new ArrayList<>();
        Contract contract = cpt.getContract();

        Set<String> processed = new HashSet<>();
        long sdrCount = l.stream().filter(p -> p.getType() != null &&
                p.getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT)).count();

        logger.info("Try to Fetch Payments using CPT for : " +
        missingTypes.keySet().stream()
                .map(ContractSummaryPaymentType::getLabel)
                .collect(Collectors.toList()));

        List<HashMap> l2 = PaymentReceiptHelper.getPaymentsReceiptTermForms(cpt);
        DateTimeFormatter formatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

        for (Map<String, Object> p : l2) {
            Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
            logger.info("payment type: " + paymentInfo.get("typeCode"));

            if(paymentInfo.get("typeCode").equals("pre_collected_payment") ||
                    paymentInfo.get("typeCode").equals("pre_collected_payment_no_vat")) {

                continue;
            }

            LocalDate startDate = formatter.parseDateTime(paymentInfo.get("startsOn").toString()).toLocalDate();
            logger.info("Start date from CPT: " + startDate.toString("yyyy-MM-dd"));

            LocalDate endDate = null;

            if(paymentInfo.get("endsOn") != null && !paymentInfo.get("endsOn").toString().isEmpty()) {
                endDate = formatter.parseDateTime(paymentInfo.get("endsOn").toString()).toLocalDate();

                if(endDate.isBefore(new LocalDate().withDayOfMonth(1))) continue;
            }

            Double additionDiscountAmount = paymentInfo.get("additionalDiscountAmount") == null
                    ? 0.0 : (Double) paymentInfo.get("additionalDiscountAmount");
            Double creditNoteAmount = paymentInfo.get("creditNoteAmount") == null
                    ? 0.0 : (Double) paymentInfo.get("creditNoteAmount");

            for(ContractSummaryPaymentType t : missingTypes.keySet()) {
                ClientMgmtPaymentDetailsDto row = new ClientMgmtPaymentDetailsDto(cpt.getContract());
                row.setPaymentMethod(getPaymentMethod(cpt.getContract(), (String)paymentInfo.get("typeCode")));

                switch (t) {
                    case MONTHLY_CURRENT_MONTH:
                        if (paymentInfo.get("typeCode").equals("monthly_payment")) {
                            if(startDate.isBefore(new LocalDate().plusMonths(1).withDayOfMonth(1))) {
                                row.setType(ContractSummaryPaymentType.MONTHLY_CURRENT_MONTH);
                            }
                        }
                        break;

                    case MONTHLY_RECURRING:
                    case FUTURE_MONTHLY_NOT_RECURRING:
                        if (paymentInfo.get("typeCode").equals("monthly_payment")) {
                            if (endDate == null) {
                                row.setType(ContractSummaryPaymentType.MONTHLY_RECURRING);

                                if(!t.equals(ContractSummaryPaymentType.MONTHLY_RECURRING)) continue;

                            } else if (endDate.isAfter(new LocalDate().dayOfMonth().withMaximumValue())) {
                                // date is in future  => setPaymentType to Future Monthly (Not Recurring)
                                row.setType(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING);

                                if(!t.equals(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING)) continue;
                            }
                        }
                        break;

                    case SDR_INSTALLMENT:
                    case UPCOMING_SDR_RENEWAL:
                        if (contract.isMaidCc()) continue;

                        if (paymentInfo.get("typeCode").equals(SDR_PAYMENT_TYPE)) {
                            if(missingTypes.get(t) != null && missingTypes.get(t)
                                    .contains(new LocalDate(startDate).toString("yyyy-MM"))) {

                                logger.info("skipping");
                                continue;
                            }

                            ContractSummaryPaymentType type = getPaymentType(cpt.getContract(), startDate.toDate(), SDR_PAYMENT_TYPE);
                            if(!type.equals(t)) continue;

                            row.setType(type);

                            if(row.getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT)) {
                                sdrCount++;
                                row.setTypeCode(String.valueOf(sdrCount));
                                row.setTypeLabel(String.valueOf(sdrCount));
                            }
                        }
                        break;

                    case INSURANCE:
                    case UPCOMING_INSURANCE_RENEWAL:
                        if (contract.isMaidCc()) continue;

                        if (paymentInfo.get("typeCode").equals(INSURANCE_PAYMENT_TYPE)) {
                            ContractSummaryPaymentType type = getPaymentType(
                                    cpt.getContract(), startDate.toDate(), INSURANCE_PAYMENT_TYPE);
                            row.setType(type);

                            if(!type.equals(t)) continue;
                        }
                        break;
                    case NON_MONTHLY:
                        if (!paymentInfo.get("typeCode").equals("monthly_payment") &&
                                !paymentInfo.get("typeCode").equals(SDR_PAYMENT_TYPE) &&
                                !paymentInfo.get("typeCode").equals(INSURANCE_PAYMENT_TYPE)) {

                            String key = paymentInfo.get("typeCode") + "_" + startDate.toString("yyyy-MM");

                            if(!s.contains(key)) {
                                PicklistItem type = Setup.getItem("TypeOfPayment", (String)paymentInfo.get("typeCode"));
                                row.setType(ContractSummaryPaymentType.NON_MONTHLY);
                                row.setTypeCode(type == null ? (String)paymentInfo.get("typeCode") : type.getCode());
                                row.setTypeLabel(type == null ? (String)paymentInfo.get("typeCode") : type.getName());
                            }
                        }
                        break;
                }

                logger.info("Row payment Type: " + row.getType());

                if(row.getType() == null) continue;
                if(processed.contains(row.getType() + "_" + row.getDueDate())) continue;

                setDtoAmountValues(row, p);
                row.setPaymentStatus(getPaymentStatus(cpt.getContract(),
                        (String)paymentInfo.get("typeCode"), startDate.toDate()).getLabel());
                row.setPaymentIncludesSalary(p.containsKey("includedWorkerSalary") && (Boolean) p.get("includedWorkerSalary"));
                row.setDueDate(startDate.isBefore(new LocalDate().plusMonths(1).withDayOfMonth(1)) &&
                        row.getType() != null &&
                        (row.getType().equals(ContractSummaryPaymentType.MONTHLY_RECURRING) ||
                                row.getType().equals(ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING))
                        ? new LocalDate().plusMonths(1).withDayOfMonth(1).toDate()
                        : startDate.isBefore(new LocalDate().withDayOfMonth(1)) && row.getType() != null && row.getType().equals(ContractSummaryPaymentType.MONTHLY_CURRENT_MONTH)
                        ? new LocalDate().withDayOfMonth(1).toDate()
                        : startDate.toDate());
                row.setDiscount(additionDiscountAmount + creditNoteAmount);
                row.setSource(ClientMgmtPaymentDetailsDto.Source.CPT);
                row.setPaymentLink(getPaymentLink(null, row, paymentInfo.get("typeCode").toString()));

                rowsList.add(row);
                processed.add(row.getType() + "_" + row.getDueDate());
            }
        }

        if(missingTypes.containsKey(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL) && contract.isMaidVisa()) {
            boolean existsSdrRenewal = rowsList.stream().anyMatch(r -> r.getType().equals(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL));

            if(!existsSdrRenewal) {
                Map<String, Object> p = l2.stream()
                        .filter(o -> {
                            Map<String, Object> paymentInfo = (Map<String, Object>) o.get("paymentInfo");

                            return paymentInfo.get("typeCode").equals(SDR_PAYMENT_TYPE) &&
                                    (Integer) paymentInfo.get("frequency") > 1;
                        })
                        .findFirst().orElse(null);

                if(p != null) {
                    Map<String, Object> paymentInfo = (Map<String, Object>) p.get("paymentInfo");
                    LocalDate startDate = formatter.parseDateTime(paymentInfo.get("startsOn").toString()).toLocalDate();

                    ClientMgmtPaymentDetailsDto optionalUpcomingSDR = new ClientMgmtPaymentDetailsDto(cpt.getContract());

                    LocalDate sd = new LocalDate(startDate);
                    while (!sd.isAfter(new LocalDate()) ||
                            sd.isBefore(new LocalDate(cpt.getContract().getStartOfContract()).plusYears(1))) {

                        sd = sd.plusMonths((Integer) paymentInfo.get("frequency"));
                    }

                    sd = sd.plusDays(Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DD_GEN_POSTPONE_START_AFTER_X_DAY)));

                    setDtoAmountValues(optionalUpcomingSDR, p);

                    optionalUpcomingSDR.setType(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL);
                    optionalUpcomingSDR.setPaymentMethod(getPaymentMethod(cpt.getContract(), (String) paymentInfo.get("typeCode")));
                    optionalUpcomingSDR.setPaymentStatus(
                            ContractSummaryPaymentStatus.FUTURE_PAYMENT_NOT_CREATED_YET.getLabel());
                    optionalUpcomingSDR.setPaymentIncludesSalary(p.containsKey("includedWorkerSalary") &&
                            (Boolean) p.get("includedWorkerSalary"));
                    optionalUpcomingSDR.setSource(ClientMgmtPaymentDetailsDto.Source.CPT);
                    optionalUpcomingSDR.setDueDate(sd.toDate());

                    rowsList.add(optionalUpcomingSDR);
                    processed.add(optionalUpcomingSDR.getType() + "_" + optionalUpcomingSDR.getDueDate());
                }
            }

        }

        if(sdrCount == 1 && contract.isMaidVisa()) {
            rowsList.forEach(row -> {
                if(row.getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT)) {
                    row.setType(ContractSummaryPaymentType.SDR);
                }
            });
        }

        Collections.sort(rowsList, (o1, o2) -> o1.getDueDate().compareTo(o2.getDueDate()));
        return rowsList;
    }

    private void setDtoAmountValues(ClientMgmtPaymentDetailsDto dto, Map<String, Object> p) {
        Double amountWithoutVat = p.get("amountWithoutVat") instanceof Long ?
                ((Long) p.get("amountWithoutVat")).doubleValue() :
                p.get("amountWithoutVat") instanceof Integer ?
                        ((Integer) p.get("amountWithoutVat")) : (Double) p.get("amountWithoutVat");
        Double amount = p.get("amount") instanceof Long ?
                ((Long) p.get("amount")).doubleValue() :
                p.get("amount") instanceof Integer ?
                        (Integer) p.get("amount") : (Double) p.get("amount");
        Double vatValue = Math.ceil(amount - amountWithoutVat);

        dto.setAmount(amount);
        dto.setVat(Math.ceil(vatValue));
    }

    /**
     * Fetch SDR Rows from Payments, if not found try to fetch from DD
     * if not found try to fetch it from CPT
     *
     * @param c: Contract
     */
    private List<ClientMgmtPaymentDetailsDto> getRecurringNonMonthlyPayments(
            Contract c,
            String paymentTypeCode,
            Map<ContractSummaryPaymentType, Set<String>> missingPayments) {

        // 1. Initial Search: Try to fetch SDR from payments
        logger.info("try to fetch SDR payments from payments");
        List<ClientMgmtPaymentDetailsDto> n = getRowsFromPaymentsForRecurringNonMonthly(c, paymentTypeCode);
        Set<String> processed = n.stream()
                .map(d -> new LocalDate(d.getDueDate()).toString("yyyy-MM"))
                .collect(Collectors.toSet());

        logger.info("try to fetch SDR payments from DD");
        n.addAll(getRowsFromDDsForRecurringNonMonthly(c, paymentTypeCode, processed));

        ClientMgmtPaymentDetailsDto dto;
        ClientMgmtPaymentDetailsDto prevDto = null;
        int j=0;

        while(j < n.size()) {
            dto = n.get(j);
            if(dto.getType().equals(ContractSummaryPaymentType.INSURANCE) ||
                    dto.getType().equals(ContractSummaryPaymentType.SDR_RENEWAL)) {

                if(prevDto != null) {
                    if(prevDto.getDueDate().compareTo(dto.getDueDate()) < 0) {
                        n.remove(prevDto);
                        prevDto = dto;
                    } else {
                        n.remove(dto);
                    }
                } else {
                    j++;
                    prevDto = dto;
                }
            } else {
                j++;
            }
        }

        logger.info("try to fetch SDR payments from generation plans");
        n.addAll(getRowsFromGenerationPlansForNonMonthly(c, paymentTypeCode));
        processed.addAll(n.stream()
                .map(row -> new LocalDate(row.getDueDate()).toString("yyyy-MM"))
                .collect(Collectors.toSet()));

        if(paymentTypeCode.equals(INSURANCE_PAYMENT_TYPE)) {
            if(n.size() == 1) {
                n.get(0).setType(ContractSummaryPaymentType.INSURANCE);
            } else if(n.isEmpty()) {
                n.add(generateRowFromWaived(c, ContractSummaryPaymentType.INSURANCE));
            }
        } else if(paymentTypeCode.equals(SDR_PAYMENT_TYPE)) {
            long installmentCount = n.stream()
                    .filter(p -> p.getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT))
                    .count();
            Set<ContractSummaryPaymentType> types = new HashSet<>();

            for(int i=0; i<n.size(); i++) {
                if(n.get(i).getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT)) {
                    if(installmentCount == 1) {
                        n.get(i).setType(ContractSummaryPaymentType.SDR);
                    } else {
                        n.get(i).setType(ContractSummaryPaymentType.SDR_INSTALLMENT);
                        n.get(i).setTypeCode(String.valueOf((i + 1)));
                        n.get(i).setTypeLabel(String.valueOf((i + 1)));
                    }
                }
                types.add(n.get(i).getType());
            }

            if(!types.contains(ContractSummaryPaymentType.SDR_RENEWAL)
                    && !types.contains(ContractSummaryPaymentType.SDR)
                    && !types.contains(ContractSummaryPaymentType.SDR_INSTALLMENT)) {

                missingPayments.put(ContractSummaryPaymentType.SDR_INSTALLMENT, null);
            } else if(!types.contains(ContractSummaryPaymentType.SDR_RENEWAL)
                    && types.contains(ContractSummaryPaymentType.SDR_INSTALLMENT)) {

                missingPayments.put(ContractSummaryPaymentType.SDR_INSTALLMENT, processed);
                logger.info("Processed: " + processed.stream().collect(Collectors.joining("; ")));
            }

            if(!types.contains(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL)) {
                missingPayments.put(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL, null);
            }
        }

        return n;
    }

    private List<ClientMgmtPaymentDetailsDto> getNonMonthlyPayments(Contract c, Set<String> processed) {
        List<ClientMgmtPaymentDetailsDto> l = new ArrayList<>();

        List<Payment> payments = fetchNonMonthlyPayments(c);
        payments.stream()
                .filter(p -> !p.getTypeOfPayment().hasTag("refund"))
                .forEach(p -> l.add(generateRowFromPayment(p)));

        processed.addAll(payments.stream()
                .map(p -> p.getTypeOfPayment().getCode() + "_" + new LocalDate(p.getDateOfPayment()).toString("yyyy-MM"))
                .collect(Collectors.toSet()));

        fetchDDNonMonthlyContractPayments(c).stream()
                .filter(cp -> !processed.contains(cp.getPaymentType().getCode() + "_" + new LocalDate(cp.getDate()).toString("yyyy-MM")))
                .forEach(cp -> {
                    l.add(generateRowFromContractPayment(cp));

                    processed.add(cp.getPaymentType().getCode() + "_" + new LocalDate(cp.getDate()).toString("yyyy-MM"));
                });

        fetchGenerationPlans(c, null).stream()
                .filter(plan -> !processed.contains(plan.getContractPaymentType().getType().getCode() + "_" +
                        new LocalDate(plan.getDDSendDate()).toString("yyyy-MM")))
                .forEach(plan -> {
                    ClientMgmtPaymentDetailsDto dto = generateRowFromGenerationPlan(plan);
                    l.add(dto);

                    processed.add(plan.getContractPaymentType().getType().getCode() + "_" +
                            new LocalDate(plan.getDDSendDate()).toString("yyyy-MM"));
                });

        return l;
    }

    /**
     * Generate Rows from DirectDebits for SDR Payments
     *
     * @param c : Contract
     */
    private List<ClientMgmtPaymentDetailsDto> getRowsFromPaymentsForRecurringNonMonthly(Contract c, String paymentTypeCode) {
        List<ClientMgmtPaymentDetailsDto> l = new ArrayList<>();

        List<Payment> payments = fetchPayments(c, paymentTypeCode, null, null, null);
        Set<String> processed = new HashSet<>();

        for(Payment payment : payments) {
            if(processed.contains(new LocalDate(payment.getDateOfPayment()).toString("yyyy-MM"))) {
                continue;
            }

            processed.add(new LocalDate(payment.getDateOfPayment()).toString("yyyy-MM"));

            // 2. If payment found: Generate a row and add it to the list
            ClientMgmtPaymentDetailsDto dto = generateRowFromPayment(payment);
            l.add(0, dto);

            if(dto.getType().equals(ContractSummaryPaymentType.SDR_RENEWAL) ||
                    dto.getType().equals(ContractSummaryPaymentType.INSURANCE)) {
                break;
            }
        }

        return l;
    }

    /**
     * Generate Rows from DirectDebits for SDR Payments
     *
     * @param c : Contract
     */
    private List<ClientMgmtPaymentDetailsDto> getRowsFromDDsForRecurringNonMonthly(
            Contract c,
            String paymentTypeCode,
            Set<String> processed) {

        List<ClientMgmtPaymentDetailsDto> l2 = new ArrayList<>();

        // 1. Initial Search: Try to fetch a current month or future DD
        List<ContractPayment> contractPayments = fetchDDContractPayments(c, paymentTypeCode, null, null);

        for(ContractPayment contractPayment : contractPayments) {
            logger.info("direct debit id : " + contractPayment.getId());
            if(processed.contains(new LocalDate(contractPayment.getDate()).toString("yyyy-MM"))) {
                logger.info("Skipping payments " + contractPayment.getDate());
                continue;
            }

            processed.add(new LocalDate(contractPayment.getDate()).toString("yyyy-MM"));

            ClientMgmtPaymentDetailsDto dto = generateRowFromContractPayment(contractPayment);
            l2.add(0, dto);

            if(dto.getType().equals(ContractSummaryPaymentType.SDR_RENEWAL) ||
                    dto.getType().equals(ContractSummaryPaymentType.INSURANCE)) {

                break;
            }
        }

        return l2;
    }

    private List<ClientMgmtPaymentDetailsDto> getRowsFromGenerationPlansForNonMonthly(
            Contract c, String paymentTypeCode) {

        List<ClientMgmtPaymentDetailsDto> l = new ArrayList<>();
        Set<String> processed = new HashSet<>();

        // 1. Initial Search: Try to fetch a future generation plan
        List<DirectDebitGenerationPlan> plans = fetchGenerationPlans(c, paymentTypeCode);

        for(DirectDebitGenerationPlan plan : plans) {
            String key = new LocalDate(plan.getDDSendDate()).toString("yyyy-MM");

            if(processed.contains(key)) continue;
            processed.add(key);

            ClientMgmtPaymentDetailsDto dto = generateRowFromGenerationPlan(plan);
            l.add(dto);

            if(paymentTypeCode.equals(INSURANCE_PAYMENT_TYPE) ||
                    dto.getType().equals(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL))
                break;
        }

        return l;
    }

    private List<ClientMgmtPaymentDetailsDto>  getMonthlyFuturePayments(Contract contract) {
        List<ClientMgmtPaymentDetailsDto> l = new ArrayList<>();

        List<ContractPayment> payments = fetchDDContractPayments(
                contract, "monthly_payment", null, null);

        DirectDebit prevDD = null;
        ContractPayment prevCp = null;

        for(ContractPayment contractPayment : payments) {
            if(new LocalDate(contractPayment.getDate()).isBefore(new LocalDate().plusMonths(1).withDayOfMonth(1))) {
                continue;
            }

            if((prevDD != null && !prevDD.equals(contractPayment.getDirectDebit()))) {
                l.add(0, generateRowFromContractPayment(prevCp));
            }

            prevDD = contractPayment.getDirectDebit();
            prevCp = contractPayment;
        }

        if(prevCp != null && (l.isEmpty() ||
                (new LocalDate(prevCp.getDate()).isBefore(
                        LocalDate.parse(l.get(0).getDueDate()))))) {

            l.add(0, generateRowFromContractPayment(prevCp));
        }

        logger.info("future monthly size: " + l.size());

        for (int i = l.size() - 2; i >= 0; i--) {
            logger.info("payment " + i + ": " + l.get(i).getAmount() + " - " + l.get(i).getDueDate());
            logger.info("payment " + (i+1) + ": " + l.get(i+1).getAmount() + " - " + l.get(i+1).getDueDate());

            if (Double.compare(l.get(i).getAmount(), l.get(i+1).getAmount()) == 0) {
                l.get(i+1).setDueDate(LocalDate.parse(l.get(i).getDueDate()).toDate());
                l.remove(i);
            }
        }

        return l;
    }

    private List<Payment> fetchPayments(Contract c, String paymentTypeCode, PaymentStatus paymentStatus, Date startDate, Date endDate) {
        List<Payment> payments = paymentRepository.findByContractAndTypeOfPaymentAndDateOfPaymentDesc(
                c, paymentTypeCode, paymentStatus, startDate, endDate);

        logger.info("payments size: " + payments.size());
        return payments;
    }

    private Payment fetchPayment(Contract c, String paymentTypeCode, PaymentStatus paymentStatus, Date startDate, Date endDate) {
        List<Payment> payments = fetchPayments(c, paymentTypeCode, paymentStatus, startDate, endDate);
        Payment payment = !payments.isEmpty() ? payments.get(0) : null;

        logger.info("payment id : " + (payment != null ? payment.getId() : "") + " for type : " + paymentTypeCode +
                ", startDate: " + startDate + ", endDate: " + endDate);
        return payment;
    }

    private List<Payment> fetchNonMonthlyPayments(Contract contract) {
        return paymentRepository.findNonMonthlyPaymentsForContractSummary(contract);
    }

    private ContractPayment fetchDDContractPayment(Contract c, String paymentTypeCode, Date startDate, Date endDate) {
        List<ContractPayment> payments = fetchDDContractPayments(c, paymentTypeCode, startDate, endDate);
        return !payments.isEmpty() ? payments.get(0) : null;
    }

    private List<ContractPayment> fetchDDContractPayments(Contract c, String paymentTypeCode, Date startDate, Date endDate) {
        return contractPaymentRepository.findDDContractPaymentByContractAndTypeAndDate(
                c, paymentTypeCode, startDate, endDate, Arrays.asList(DirectDebitStatus.CANCELED,
                        DirectDebitStatus.PENDING_FOR_CANCELLATION, DirectDebitStatus.EXPIRED,
                        DirectDebitStatus.REJECTED));
    }

    private List<ContractPayment> fetchDDNonMonthlyContractPayments(Contract contract) {
        return contractPaymentRepository.findDDNonMonthlyContractPayments(contract,
                Arrays.asList(DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION,
                        DirectDebitStatus.EXPIRED, DirectDebitStatus.REJECTED));
    }

    private List<DirectDebitGenerationPlan> fetchGenerationPlans(Contract c, String paymentTypeCode) {
        return paymentTypeCode != null
                ? directDebitGenerationPlanRepository.findByContractAndDdGenerationPlanStatusAndContractPaymentType_Type_CodeOrderByDdSendDateAsc(
                        c, DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING, paymentTypeCode)
                : directDebitGenerationPlanRepository.findByNonMonthlyPlansByContractOrderByDdGenerationDateAsc(c);
    }

    private ContractSummaryPaymentStatus getRejectedDdPaymentStatus(DirectDebit directDebit) {
        DirectDebit lastDD = directDebit.getDirectDebitRejectionToDo().getLastDirectDebit();
        DirectDebitStatus status = lastDD.getCategory().equals(DirectDebitCategory.A) ?
                lastDD.getMStatus() : lastDD.getStatus();

        if (status.equals(DirectDebitStatus.PENDING)) {
            return ContractSummaryPaymentStatus.REJECTED_PENDING_BANK_RESPONSE;
        } else if (status.equals(DirectDebitStatus.IN_COMPLETE)) {
            return ContractSummaryPaymentStatus.REJECTED_PENDING_CLIENT_TO_SIGN;
        }

        return null;
    }

    private ContractSummaryPaymentStatus getPaymentStatus() {
        return getPaymentStatus(null, null, null, null, null);
    }

    private ContractSummaryPaymentStatus getPaymentStatus(Payment p) {
        return getPaymentStatus(null, p, null, null, null);
    }

    private ContractSummaryPaymentStatus getPaymentStatus(ContractPayment cp) {
        return getPaymentStatus(null, null, cp, null, null);
    }

    private ContractSummaryPaymentStatus getPaymentStatus(Contract ct, String paymentTypeCode, Date startDate) {
        return getPaymentStatus(ct, null, null, paymentTypeCode, startDate);
    }

    // Helper Functions for Payment
    private ContractSummaryPaymentStatus getPaymentStatus(
            Contract contract,
            Payment payment,
            ContractPayment cp,
            String paymentTypeCode,
            Date startDate) {

        if (payment != null) {
            if(Double.compare(payment.getAmountOfPayment(), 0.0) == 0) {
                return ContractSummaryPaymentStatus.WAIVED;
            }

            switch (payment.getStatus()) {
                case RECEIVED:
                    return ContractSummaryPaymentStatus.RECEIVED;
                case BOUNCED:
                    return ContractSummaryPaymentStatus.BOUNCED;
                case PRE_PDP:
                    if (!payment.getMethodOfPayment().equals(PaymentMethod.DIRECT_DEBIT)) {
                        return ContractSummaryPaymentStatus.PENDING_COLLECTION;
                    }

                    if(payment.getDirectDebit() != null &&
                            payment.getDirectDebit().getDirectDebitRejectionToDo() != null) {

                        ContractSummaryPaymentStatus status = getRejectedDdPaymentStatus(payment.getDirectDebit());
                        if(status != null) return status;
                    }
                    return ContractSummaryPaymentStatus.PENDING_BANK_APPROVAL;
                case PDC:
                    return ContractSummaryPaymentStatus.PENDING_COLLECTION;
            }
        } else if(cp != null) {
            if(Double.compare(cp.getAmount(), 0.0) == 0) {
                return ContractSummaryPaymentStatus.WAIVED;
            }

            // FETCH RELATED DIRECT_DEBIT BY DATE AND CONTRACT
            DirectDebit directDebit = cp.getDirectDebit();
            contract = cp.getContractPaymentTerm().getContract();
            DirectDebitStatus ddStatus = directDebit.getCategory().equals(DirectDebitCategory.A)
                    ? directDebit.getMStatus()
                    : directDebit.getStatus();

            switch (ddStatus) {
                case CONFIRMED:
                    return ContractSummaryPaymentStatus.PENDING_COLLECTION;
                case IN_COMPLETE: {
                    if(directDebit.getDirectDebitRejectionToDo() != null) {
                        ContractSummaryPaymentStatus status = getRejectedDdPaymentStatus(directDebit);
                        if(status != null) return status;
                    }

                    ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                    if (cpt.getIsEidRejected() || cpt.getIsIBANRejected() || cpt.getIsAccountHolderRejected()) {
                        return ContractSummaryPaymentStatus.INCOMPLETE_DOCUMENTS_REJECTED;
                    } else {
                        return ContractSummaryPaymentStatus.INCOMPLETE_MISSING_SIGNATURE;
                    }
                }
                case PENDING:
                    if(directDebit.getDirectDebitRejectionToDo() != null) {
                        ContractSummaryPaymentStatus status = getRejectedDdPaymentStatus(directDebit);
                        if(status != null) return status;
                    }
                    return ContractSummaryPaymentStatus.PENDING_BANK_APPROVAL;
                case PENDING_DATA_ENTRY:
                    return ContractSummaryPaymentStatus.PENDING_DATA_ENTRY;
            }
        }

        if(paymentTypeCode != null && contract != null && startDate != null &&
                paymentTypeCode.equals("monthly_payment") &&
                paymentRepository.existsByContractAndRecurringTrue(contract, startDate)) {

            return ContractSummaryPaymentStatus.PENDING_COLLECTION;
        }

        return ContractSummaryPaymentStatus.FUTURE_PAYMENT_NOT_CREATED_YET;
    }

    private String getPaymentMethod(Contract contract, String paymentTypeCode) {
        return getPaymentMethod(contract, null, null, null, paymentTypeCode);
    }

    private String getPaymentMethod(Contract contract, DirectDebitGenerationPlan plan) {
        return getPaymentMethod(contract, null, null, plan, null);
    }

    private String getPaymentMethod(Contract contract, Payment p) {
        return getPaymentMethod(contract, p, null, null, null);
    }

    private String getPaymentMethod(Contract contract, ContractPayment cp) {
        return getPaymentMethod(contract, null, cp, null, null);
    }

    private String getPaymentMethod(
            Contract contract,
            Payment payment,
            ContractPayment cp,
            DirectDebitGenerationPlan plan,
            String paymentTypeCode) {

        if (payment != null) {
            switch (payment.getMethodOfPayment()) {
                case DIRECT_DEBIT:
                    return ContractSummaryPaymentMethod.DIRECT_DEBIT.getLabel();
                case CARD:
                    return ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
                case WIRE_TRANSFER:
                    return ContractSummaryPaymentMethod.WIRE_TRANSFER.getLabel();
                case CASH:
                    return ContractSummaryPaymentMethod.CASH.getLabel();
                case ADJUSTMENT:
                    return ContractSummaryPaymentMethod.ADJUSTMENT.getLabel();
            }

            if (payment.getMethodOfPayment().equals(PaymentMethod.CARD) &&
                    payment.getTypeOfPayment().getCode().equals("monthly_payment")) {

                List<Map<String, Object>> todos = contractPaymentConfirmationToDoRepository
                        .getSourceFromToDoByPaymentId(payment.getId());

                if (!todos.isEmpty()) {
                    ContractPaymentConfirmationToDo.Source source = ((ContractPaymentConfirmationToDo) todos.get(0).get("todoSource")).getSource();
                    String flowName = "" ;

                    switch (source) {
                        case AFTER_CASH_FLOW:
                            flowName = "IPAM";
                            break;
                        case CLIENT_PAYING_VIA_Credit_Card:
                            flowName = "Credit Card";
                            break;
                        case BOUNCED_PAYMENT_FLOW:
                            flowName = "Bouncing";
                            break;
                        case ONE_MONTH_AGREEMENT:
                            flowName = "One Month Agreement";
                            break;
                        default:
                            flowName = "";
                            break;
                    }

                    return !flowName.isEmpty()
                            ? ContractSummaryPaymentMethod.CREDIT_CARD_FLOW.getLabel().replace("@FlowName@", flowName)
                            : ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
                }
            }
        }

        // Fetch DDs
        if (cp != null) {
            return ContractSummaryPaymentMethod.DIRECT_DEBIT.getLabel();
        }

        if(paymentTypeCode == null) {
            if(plan != null) {
                paymentTypeCode = plan.getContractPaymentType().getType().getCode();
            } else if(payment != null) {
                paymentTypeCode = payment.getTypeOfPayment().getCode();
            } else if(cp != null) {
                paymentTypeCode = cp.getPaymentType().getCode();
            }
        }

        if((plan != null && paymentTypeCode.equals(INSURANCE_PAYMENT_TYPE)) ||
                (paymentTypeCode != null && paymentTypeCode.equals(INSURANCE_PAYMENT_TYPE))) {

            return ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
        }

        // Set based on Flow
        if (Setup.getApplicationContext().getBean(FlowProcessorService.class)
                .existsRunningFlow(contract, FlowEventConfig.FlowEventName.EXTENSION_FLOW)) {

            return paymentTypeCode != null && paymentTypeCode.equals("monthly_payment")
                    ? ContractSummaryPaymentMethod.CREDIT_CARD_FLOW.getLabel()
                            .replace("@FlowName@", "Extension")
                    :ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
        }

        if (contract.isPayingViaCreditCard() && contract.isOneMonthAgreement()) {
            return paymentTypeCode != null && paymentTypeCode.equals("monthly_payment")
                    ? ContractSummaryPaymentMethod.CREDIT_CARD_FLOW.getLabel()
                            .replace("@FlowName@", "One Month Agreement")
                    :ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
        }

        if (contract.isPayingViaCreditCard()) {
            return paymentTypeCode != null && paymentTypeCode.equals("monthly_payment")
                    ? ContractSummaryPaymentMethod.CREDIT_CARD_FLOW.getLabel()
                            .replace("@FlowName@", "Credit Card")
                    :ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
        }

        if (ContractService.isIpam(contract)) {
            return paymentTypeCode != null && paymentTypeCode.equals("monthly_payment")
                    ? ContractSummaryPaymentMethod.CREDIT_CARD_FLOW.getLabel()
                            .replace("@FlowName@", "IPAM")
                    :ContractSummaryPaymentMethod.CREDIT_CARD.getLabel();
        }

        return ContractSummaryPaymentMethod.DIRECT_DEBIT.getLabel();
    }

    private ContractSummaryPaymentType getPaymentType(
            Contract contract,
            Payment payment,
            ContractPayment cp,
            DirectDebitGenerationPlan plan) {

        return getPaymentType(contract, payment, cp, plan, null, null);
    }

    private ContractSummaryPaymentType getPaymentType(
            Contract contract,
            Date d,
            String t) {

        return getPaymentType(contract, null, null, null, d, t);
    }

    private ContractSummaryPaymentType getPaymentType(
            Contract contract,
            Payment payment,
            ContractPayment cp,
            DirectDebitGenerationPlan plan,
            Date d,
            String t) {

        String paymentTypeCode = payment != null
                ? payment.getTypeOfPayment().getCode()
                : cp != null
                ? cp.getPaymentType().getCode()
                : plan != null
                ? plan.getContractPaymentType().getType().getCode()
                : t;

        Date paymentDate = payment != null
                ? payment.getDateOfPayment()
                : cp != null
                ? cp.getDate()
                : plan != null
                ? plan.getDDSendDate()
                : d;

        switch (paymentTypeCode) {
            case "monthly_payment": {
                if (paymentDate != null) {
                    if (new LocalDate(paymentDate).toString("yyyy-MM").equals(
                            new LocalDate().toString("yyyy-MM"))) {

                        return ContractSummaryPaymentType.MONTHLY_CURRENT_MONTH;
                    } else if (new LocalDate(paymentDate).toString("yyyy-MM").equals(
                            new LocalDate().minusMonths(1).toString("yyyy-MM"))) {

                        return ContractSummaryPaymentType.MONTHLY_PREVIOUS_MONTH;
                    }
                }

                if (cp != null && cp.getDirectDebit().getCategory().equals(DirectDebitCategory.A)) {
                    return ContractSummaryPaymentType.FUTURE_MONTHLY_NOT_RECURRING;
                } else if (cp != null && cp.getDirectDebit().getCategory().equals(DirectDebitCategory.B)) {
                    return ContractSummaryPaymentType.MONTHLY_RECURRING;
                }

                break;
            }

            case "insurance":
                if(payment != null || cp != null) {
                    return ContractSummaryPaymentType.INSURANCE;
                } else {
                    return ContractSummaryPaymentType.UPCOMING_INSURANCE_RENEWAL;
                }

            case "same_day_recruitment_fee":
                if(new LocalDate(paymentDate).isAfter(
                        new LocalDate(contract.getStartOfContract()).plusYears(1))) {

                    if(payment != null || cp != null) {
                        return ContractSummaryPaymentType.SDR_RENEWAL;
                    } else {
                        return ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL;
                    }
                } else {
                    return ContractSummaryPaymentType.SDR_INSTALLMENT;
                }
        }

        return ContractSummaryPaymentType.NON_MONTHLY;
    }

    private String getPaymentLink(
            Payment payment,
            ClientMgmtPaymentDetailsDto row,
            String paymentType) {

        List<ContractPaymentConfirmationToDo> todos = new ArrayList<>();

        if(payment != null) {
            todos = contractPaymentConfirmationToDoRepository.findToDosByPaymentsCheckedByContract(
                    payment.getId());
        } else if (row != null) {
            todos = contractPaymentConfirmationToDoRepository.findOnlineMonthlyCardPaymentTodo(
                    row.getContract(), new LocalDate(row.getDueDate()).toDate(), row.getAmount(), paymentType);
        }

        if (todos.isEmpty()) return "NULL";
        return contractPaymentConfirmationToDoService.getPayingViaCreditCardLink(todos.get(0));
    }

    private String getRefactoredPaymentStatus(Contract c, ContractSummaryPaymentStatus status) {
        if (status.equals(ContractSummaryPaymentStatus.INCOMPLETE_DOCUMENTS_REJECTED)) {
            ContractPaymentTerm cpt = c.getActiveContractPaymentTerm();
            StringBuilder documentsString = new StringBuilder();
            if (cpt.getIsEidRejected() != null && cpt.getIsEidRejected()) {
                documentsString.append("EID");
            }

            if (cpt.getIsIBANRejected() != null && cpt.getIsIBANRejected()) {
                documentsString.append(documentsString.length() > 0 ? ", IBAN" : "IBAN");
            }

            if (cpt.getIsAccountHolderRejected() != null && cpt.getIsAccountHolderRejected()) {
                documentsString.append(documentsString.length() > 0 ? ", Account Name" : "Account Name");
            }
            return status.getLabel().replace("@documents@", documentsString);
        }

        return status.getLabel();
    }


    // Generate Row Functions
    private ClientMgmtPaymentDetailsDto generateRowFromContractPayment(ContractPayment cp) {
        ClientMgmtPaymentDetailsDto r = new ClientMgmtPaymentDetailsDto(cp.getContractPaymentTerm().getContract());

        logger.info("ContractPayment id : " + cp.getId());

        r.setType(getPaymentType(cp.getContractPaymentTerm().getContract(), null, cp, null));
        if(!cp.getPaymentType().getCode().equals(SDR_PAYMENT_TYPE)) {
            r.setTypeCode(cp.getPaymentType().getCode());
            r.setTypeLabel(cp.getPaymentType().getName());
        }
        r.setPaymentMethod(getPaymentMethod(cp.getContractPaymentTerm().getContract(), cp));
        r.setPaymentStatus(getRefactoredPaymentStatus(cp.getContractPaymentTerm().getContract(),
                getPaymentStatus(cp)));
        r.setDateReceived("NULL");
        r.setAmount(cp.getAmount());
        r.setDiscount(cp.getAdditionalDiscountAmount() + cp.getMoreAdditionalDiscount());
        r.setIsWaived(Double.compare(cp.getAmount(), 0.0) == 0);
        r.setDueDate(cp.getDate());
        r.setPaymentLink(getPaymentLink(null, r, cp.getPaymentType().getCode()));
        r.setVat(Math.ceil(cp.getVat()));
        r.setPaymentIncludesSalary(cp.getIncludeWorkerSalary());
        r.setSource(ClientMgmtPaymentDetailsDto.Source.CONTRACT_PAYMENT);
        return r;
    }

    private ClientMgmtPaymentDetailsDto generateRowFromGenerationPlan(DirectDebitGenerationPlan plan) {
        ClientMgmtPaymentDetailsDto r = new ClientMgmtPaymentDetailsDto(plan.getContract());

        logger.info("plan id : " + plan.getId());

        r.setType(getPaymentType(plan.getContract(), null, null, plan));
        if(!plan.getContractPaymentType().getType().getCode().equals(SDR_PAYMENT_TYPE)) {
            r.setTypeCode(plan.getContractPaymentType().getType().getCode());
            r.setTypeLabel(plan.getContractPaymentType().getType().getName());
        }
        r.setPaymentMethod(getPaymentMethod(plan.getContract(), plan));
        r.setPaymentStatus(getPaymentStatus().getLabel());
        r.setDateReceived("NULL");
        r.setAmount(plan.getAmount());
        r.setDiscount(plan.getAdditionalDiscountAmount() + plan.getMoreAdditionalDiscount());
        r.setIsWaived(false);
        r.setDueDate(plan.getDDSendDate());
        r.setPaymentLink("NULL");
        r.setVat(plan.getContractPaymentType().getType().hasTag("NO_VAT")
                ? 0.0
                : Math.ceil(plan.getAmount() - DiscountsWithVatHelper.getAmountWithoutVat(plan.getAmount())));
        r.setPaymentIncludesSalary(false);
        r.setSource(ClientMgmtPaymentDetailsDto.Source.GENERATION_PLAN);

        return r;
    }

    private ClientMgmtPaymentDetailsDto generateRowFromPayment(Payment payment) {
        ClientMgmtPaymentDetailsDto r = new ClientMgmtPaymentDetailsDto(payment.getContract());

        logger.info("payment id : " + payment.getId());
        ContractPayment cp = paymentService.getContractPayment(payment); // MC-28 to review

        r.setType(getPaymentType(payment.getContract(), payment, null, null));
        if(!payment.getTypeOfPayment().getCode().equals(SDR_PAYMENT_TYPE)) {
            r.setTypeCode(payment.getTypeOfPayment().getCode());
            r.setTypeLabel(payment.getTypeOfPayment().getName());
        }
        r.setPaymentMethod(getPaymentMethod(payment.getContract(), payment));
        r.setPaymentStatus(getPaymentStatus(payment).getLabel());
        r.setAmount(payment.getAmountOfPayment() == 0.0 && payment.getReplacementFor() != null
                ? payment.getReplacementFor().getAmountOfPayment()
                : payment.getAmountOfPayment());
        r.setDiscount(cp == null ? 0.0 : cp.getAdditionalDiscountAmount() + cp.getMoreAdditionalDiscount());
        r.setIsWaived(Double.compare(payment.getAmountOfPayment(), 0.0) == 0);
        r.setVat(Math.ceil(payment.getVat()));
        r.setPaymentLink(getPaymentLink(payment, null, null));
        r.setPaymentIncludesSalary(payment.getIncludeWorkerSalary());
        r.setSource(ClientMgmtPaymentDetailsDto.Source.PAYMENT);
        if (payment.getStatus().equals(PaymentStatus.RECEIVED)) {
            r.setDateReceived(new LocalDate(payment.getDateChangedToReceived()).toString("yyyy-MM-dd"));
            r.setReceived(true);
        }

        // Handle Tokenized Payment Status for Recurring Payments
        if (payment.getRecurring() && payment.getMethodOfPayment().equals(PaymentMethod.CARD) &&
            r.getType().equals(ContractSummaryPaymentType.MONTHLY_CURRENT_MONTH)) {

            ContractSummaryTokenizedPaymentStatus status = null;
            if (payment.getStatus().equals(PaymentStatus.RECEIVED)) {
                status = ContractSummaryTokenizedPaymentStatus.RECEIVED;
            } else {
                // Fetch the to-do on Payment and check if this to-do linked with FailureFLow or not
                List<Map<String, Object>> toDos = contractPaymentConfirmationToDoRepository.findFlowsRelatedToRecurringPayment(
                        payment.getId(), Arrays.asList(
                                INSUFFICIENT_FUNDS,
                                EXCEEDING_DAILY_LIMITS,
                                ACCOUNT_ISSUE,
                                CC_OTHER_ISSUES,
                                EXPIRED_CARD));

                if (!toDos.isEmpty()) {
                    logger.info("toDo id : " + toDos.get(0).get("toDoId"));
                    switch ((FlowSubEventConfig.FlowSubEventName) toDos.get(0).get("subEventName")) {
                        case INSUFFICIENT_FUNDS:
                            status = ContractSummaryTokenizedPaymentStatus.INSUFFICIENT_FUNDS;
                            break;
                        case EXCEEDING_DAILY_LIMITS:
                            status = ContractSummaryTokenizedPaymentStatus.EXCEEDS_CARD_LIMIT;
                            break;
                        case ACCOUNT_ISSUE:
                            status = ContractSummaryTokenizedPaymentStatus.ACCOUNT_ISSUE;
                            break;
                        case CC_OTHER_ISSUES:
                            status = ContractSummaryTokenizedPaymentStatus.OTHERS;
                            break;
                        case EXPIRED_CARD:
                            status = ContractSummaryTokenizedPaymentStatus.EXPIRY_CREDIT_CARD;
                            break;
                    }
                } else if (payment.getStatus().equals(PaymentStatus.PDC)) {
                    status = ContractSummaryTokenizedPaymentStatus.PENDING_COLLECTION;
                }
            }

            r.setTokenizedPaymentStatus(status != null ? status.getLabel() : "NULL");
        }

        // Handle Adjustment Payment
        if (r.getPaymentMethod().equals(ContractSummaryPaymentMethod.ADJUSTMENT.getLabel())) {
            r.setIsWaived(true);
            r.setDateReceived(new LocalDate(payment.getCreationDate()).toString("yyyy-MM-dd"));
            r.setPaymentStatus(ContractSummaryPaymentStatus.WAIVED.getLabel());
        }
        r.setDueDate(payment.getDateOfPayment());

        return r;
    }

    private ClientMgmtPaymentDetailsDto generateRowFromWaived(Contract c, ContractSummaryPaymentType type) {
        ClientMgmtPaymentDetailsDto r = new ClientMgmtPaymentDetailsDto(c);
        r.setType(type);
        r.setPaymentStatus(ContractSummaryPaymentStatus.WAIVED.getLabel());
        r.setIsWaived(true);
        r.setSource(ClientMgmtPaymentDetailsDto.Source.NOT_EXISTING);

        return r;
    }

    /**
     * Third Section
     */
    private Map<String, Object> fillGeneralPaymentInfoThirdSection(Contract contract, ContractPaymentTerm cpt, List<ClientMgmtPaymentDetailsDto> l) {
        Map<String, Object> generalPaymentInfo = new HashMap<>();

        // 3.1 First Salary Payment Due Date
        DateTime d = new DateTime(calculateDiscountsWithVatService.getDiscountStartDateInMillis(cpt));
        generalPaymentInfo.put("firstSalaryPaymentDueDate",
                d.dayOfMonth().withMinimumValue().toLocalDate().toString("yyyy-MM-dd"));

        if (ContractService.isPreCollectedSalary(contract)) {
            List<Map<String, Object>> preCollectedPayments = paymentRepository.findPaymentsByContractAndTypeAndStatus(
                    contract, true,
                    Arrays.asList("monthly_payment", "pre_collected_payment", "pre_collected_payment_no_vat"),
            false);

            if (!preCollectedPayments.isEmpty()) {
                generalPaymentInfo.put("firstSalaryPaymentDueDate",
                        new LocalDate(preCollectedPayments.get(0).get("date")).toString("yyyy-MM-dd"));
            }
        } else {
            List<Map<String, Object>> payments = paymentRepository.findPaymentsByContractAndTypeAndStatus(
                    contract, true, Arrays.asList("monthly_payment"), false);

            if (!payments.isEmpty()) {
                generalPaymentInfo.put("firstSalaryPaymentDueDate",
                        new LocalDate(payments.get(0).get("date")).toString("yyyy-MM-dd"));
            }
        }

        // 3.2 WPS Processing Amount
        Double wpsAmountWithVat = contractPaymentTypeRepository.findWpsAmountWithVatByContractId(cpt.getContract().getId());
        generalPaymentInfo.put("wpsProcessingAmount", wpsAmountWithVat != null ? (int) Math.round(wpsAmountWithVat) : "NULL");

        // 3.3 Vat On Salary
        generalPaymentInfo.put("vatOnSalary", contract.isMaidCc() || contract.isWorkerSalaryVatted());

        // 3.4 Is Pre-Collected
        generalPaymentInfo.put("isPreCollected", ContractService.isPreCollectedSalary(contract));

        // 3.5 Try get DD Signature Link
        String signDDLink = null;
        if (contractPaymentTermServiceNew.allowSigningDD(contract) && !contract.getStatus().equals(ContractStatus.CANCELLED)) {
            AccountingLink a = accountingLinkRepository
                    .findTopByContractIdAndTypeOrderByCreationDateDesc(contract.getId(), AccountingLink.AccountingLinkType.SIGN_DD_WEB_PAGE);

            if (a != null) {
                logger.info("link found : " + a.getShortenedLink() + " , id : " + a.getId());
                signDDLink = a.getShortenedLink();
            }
        }
        generalPaymentInfo.put("ddSignLink", signDDLink != null && !signDDLink.isEmpty() ? signDDLink : "NULL");

        // 3.6 Paid End Date
        generalPaymentInfo.put("paidEndDate", new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd"));

        // 3.7 Bank Account Change
        // 3.8 Latest Approved Bank Info
        List<Object[]> bankInfo = directDebitRepository.getLatestBankInfoByContract(contract);
        Object[] o = bankInfo.isEmpty() ? null : bankInfo.get(bankInfo.size()-1);
        String iban = o != null && o[0] != null ? (String) o[0] : "NULL";

        generalPaymentInfo.put("bankAccountChanged", bankInfo.size() > 1);
        generalPaymentInfo.put("latestApprovedBankInfo", new HashMap<String, Object>() {{
            put("ibanNumber", iban);
            put("bankName", o != null ? o[1] : "NULL");
            put("confirmBankInfoDate", o != null ? o[2] : "NULL");
            put("accountName", o != null ? o[3] : "NULL");
        }});

        // 3.9 Payment Method
        generalPaymentInfo.put("paymentMethod", Setup.getApplicationContext()
                .getBean(FlowProcessorService.class)
                .isPayingViaCreditCard(contract) ? "Credit Card" : "Monthly Bank Payment Form");

        // 3.10 Client Approved Tokenization
        // possible values : null, true, false
        generalPaymentInfo.put("clientApprovedTokenization", generalPaymentInfo.get("paymentMethod").equals("Credit Card") &&
                contract.getAllowRecurring() && cpt.getSourceId() != null &&
                cpt.getSourceAmount() != null && cpt.getSourceInfo() != null);

        // 3.11 Last Refund
        generalPaymentInfo.put("lastRefund", getLastRefundInformation(contract));

        // 3.12 Credit Card Expiry Date
        generalPaymentInfo.put("creditCardExpiryDate", Boolean.TRUE.equals(generalPaymentInfo.get("clientApprovedTokenization")) ?
                cpt.getSourceInfo().get("expiryDateFormatted") : "NULL");

        if (!contract.isMaidCc()) {
            // 3.13 Is Travel Assist
            boolean isTravelAssist = paymentRepository.existsReceivedPaymentByContractAndType(
                    contract, "travel_assist");
            generalPaymentInfo.put("isTravelAssist", isTravelAssist);

            // 3.14 Payment Plan Affected
            generalPaymentInfo.put("paymentPlanAffected", isTravelAssist && isPaymentPlanAffected(contract));

            // 3.15 SDR Installments Count (0, 2, 3, 4)
            long sdrCount = l.stream().filter( e -> e.getType() != null &&
                            e.getType().equals(ContractSummaryPaymentType.SDR_INSTALLMENT))
                    .count();
            generalPaymentInfo.put("sdrInstallmentCount", sdrCount == 1 ? 0 : sdrCount);
        }



        // 3.16 PED - 2  days with current date check
        LocalDate ped = new LocalDate(generalPaymentInfo.get("paidEndDate"));
        generalPaymentInfo.put("currentDateGreaterOrEqualPED_2",
                ped.minusDays(2).isBefore(new LocalDate()) ||
                ped.minusDays(2).isEqual(new LocalDate()));

        // 3.17 PED - 4 value
        generalPaymentInfo.put("ped_4", ped.minusDays(4).toString("yyyy-MM-dd"));

        // 3.18 Extract the last 4 digits from the IBAN
        generalPaymentInfo.put("last4DigitsInIban", !iban.equals("NULL")
                ? iban.substring(iban.length() - 4)
                : "NULL");

        // 3.19 Salary check
        generalPaymentInfo.put("salaryLessThan1200", contract.getHousemaid() != null &&
                contract.getHousemaid().getBasicSalary() != null &&
                contract.getHousemaid().getBasicSalary() < 1200);

        // 3.20 fill lastSalaryPaymentDate info
        Map<String, Boolean> m = fillLastSalaryPaymentDateInfo(contract, generalPaymentInfo);
        generalPaymentInfo.put("TodayIsAfterSalaryPaymentDate", m.get("TodayIsAfterSalaryPaymentDate"));
        generalPaymentInfo.put("TodayIsSalaryPaymentDate", m.get("TodayIsSalaryPaymentDate"));

        // 3.21 sdr_amount_less_than_sdr_renewal_amount check
        generalPaymentInfo.put("sdrAmountLessThanSdrRenewalAmount", isSdrAmountLessThanSdrRenewalAmount(l));

        // 3.22 SDR amount check
        generalPaymentInfo.put("sdrAmountGreaterThan8500", l.stream()
                .filter(r -> r.getPaymentType().toLowerCase().contains("sdr") &&
                        !r.getType().equals(ContractSummaryPaymentType.UPCOMING_SDR_RENEWAL))
                .mapToDouble(ClientMgmtPaymentDetailsDto::getAmount).sum() > 8925);

        // 3.23 VATAppliedOnSalary
        Double vatAppliedOnSalary = null;
        if (contract.isWorkerSalaryVatted() && contract.getWorkerSalaryWithoutVat() != null) {
            vatAppliedOnSalary = contract.getWorkerSalaryWithVat() - contract.getWorkerSalaryWithoutVat();
        }
        generalPaymentInfo.put("vatAppliedOnSalary", vatAppliedOnSalary == null ? "NULL" : vatAppliedOnSalary);

        // 3.24 WPSFeeExcludingVAT
        // 3.25 VATAppliedOnWPS
        Double wpsFeeExcludingVat = 0.0;
        Double vatAppliedOnWPS = 0.0;
        if (wpsAmountWithVat != null && wpsAmountWithVat > 0) {
            wpsFeeExcludingVat = DiscountsWithVatHelper.getAmountWithoutVat(wpsAmountWithVat);
            vatAppliedOnWPS = wpsAmountWithVat - wpsFeeExcludingVat;
        }
        generalPaymentInfo.put("wpsFeeExcludingVAT", wpsFeeExcludingVat);
        generalPaymentInfo.put("vatAppliedOnWPS", vatAppliedOnWPS);

        return generalPaymentInfo;
    }

    private boolean isSdrAmountLessThanSdrRenewalAmount(List<ClientMgmtPaymentDetailsDto> l) {
        // search in l if no sdr renewal return false
        ClientMgmtPaymentDetailsDto sdrRenewal = l.stream()
                .filter(r -> r.getType().equals(ContractSummaryPaymentType.SDR_RENEWAL))
                .findFirst().orElse(null);

        if (sdrRenewal == null) return false;

        List<Map<String, Object>> sdrRenewalPayments = paymentRepository.findSdrRenewalReceivedPayments(
                sdrRenewal.getContract(),
                new LocalDate(sdrRenewal.getDueDate()).minusYears(1).toDate());

        if (sdrRenewalPayments.isEmpty()) return false;

        Double amount = 0.0D;
        // sum amounts from most recent to oldest payments if the difference in date between each two payments is one month (SDR installment)
        for(int i = 0; i < sdrRenewalPayments.size(); i++) {
            Map<String, Object> m = sdrRenewalPayments.get(i);
            amount += (Double) m.get("amount");

            if ((i + 1) == sdrRenewalPayments.size()) break;

            LocalDate prevDate = new LocalDate(sdrRenewalPayments.get(i + 1).get("date"));
            LocalDate currDate = new LocalDate(m.get("date"));
            if (!prevDate.plusMonths(1).toString("yyyy-MM")
                    .equals(currDate.toString("yyyy-MM"))) break;
        }

        return amount < sdrRenewal.getAmount();
    }

    private Map<String, Boolean> fillLastSalaryPaymentDateInfo(Contract contract, Map<String, Object> generalPaymentInfo) {
        Map<String, Boolean> result = new HashMap<>();
        result.put("TodayIsAfterSalaryPaymentDate", false);
        result.put("TodayIsSalaryPaymentDate", false);

        if (contract.getHousemaid() == null) {
            return result;
        }

        Map<String, Object> payrollLastSalaryResponse =
                Optional.ofNullable(getPayrollLastSalaryResponse(contract.getHousemaid()))
                        .orElseGet(HashMap::new);

        String paidOnDate = (String) payrollLastSalaryResponse.getOrDefault("paidOnDate", "");

        if (paidOnDate == null || paidOnDate.isEmpty()) {
            return result;
        }

        LocalDate salaryPaymentDate;
        try {
            DateTimeFormatter formatter = DateTimeFormat.forPattern("dd MMMM, yyyy");
            salaryPaymentDate = formatter.parseLocalDate(paidOnDate);
        } catch (Exception e) {
            e.printStackTrace();
            logger.warning("Failed to parse date: " + paidOnDate + ". Error: " + e.getMessage());
            try {
                salaryPaymentDate = new LocalDate(paidOnDate);
            } catch (Exception e2) {
                e2.printStackTrace();
                logger.severe("Failed to parse date with fallback format: " + paidOnDate + ". Error: " + e2.getMessage());
                return result;
            }
        }

        LocalDate today = new LocalDate();

        result.put("TodayIsAfterSalaryPaymentDate", today.isAfter(salaryPaymentDate));
        result.put("TodayIsSalaryPaymentDate", today.isEqual(salaryPaymentDate));

        return result;
    }

    /** From Visa module */
    public Map<String, Object> getPayrollLastSalaryResponse(Housemaid housemaid) {
        try {
            List<Map<String, Object>> payrollSalaryResponse = interModuleConnector.get(
                    "/payroll/HousemaidPayroll/" + housemaid.getId() + "/getHistoryLog?monthsCount=1", List.class);
            if (payrollSalaryResponse != null && !payrollSalaryResponse.isEmpty()) {
                return payrollSalaryResponse.get(0);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private Map<String, Object> getLastRefundInformation(Contract contract) {
        Map<String, Object> lastRefund = new HashMap<String, Object>() {{
            put("date", "NULL");
            put("amount", "NULL");
            put("status", "NULL");
            put("refundMethod", "NULL");
            put("dateOfStatusChanged", "NULL");
            put("requester", "NULL");
        }};
        ClientRefundToDo refund = clientRefundTodoRepository.findFirstByContractAndStatusNotOrderByCreationDateDesc(contract,
                ClientRefundStatus.STOPPED);

        if (refund == null) return lastRefund;

        List<String> botsLoginNames = Arrays.stream(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_BOT_USERS_LOGIN_NAMES)
                .split(",")).filter(l -> !l.isEmpty()).collect(Collectors.toList());

        lastRefund.put("date", new LocalDate(refund.getCreationDate()).toString("yyyy-MM-dd"));
        lastRefund.put("amount", refund.getAmount());
        lastRefund.put("status", refund.getStatus().getLabel());
        lastRefund.put("refundMethod", refund.getMethodOfPayment().getLabel());
        lastRefund.put("dateOfStatusChanged", new LocalDate(new Date(refund.getStatusChangeDate().getTime())).toString("yyyy-MM-dd"));
        lastRefund.put("requester", refund.getCreator().isAdmin() || refund.isAutomaticRefund()
                ? "Admin"
                : botsLoginNames.contains(refund.getCreator().getLoginName())
                ? "bot"
                : refund.getCreator().getFullName());
        return lastRefund;
    }

    private boolean isPaymentPlanAffected(Contract contract) {
        boolean existsActivePayment = paymentRepository.existsActiveMonthlyPayment(contract);
        if(existsActivePayment) return true;

        return contractPaymentRepository.existsActiveMonthlyDd(contract,
                Arrays.asList(DirectDebitStatus.CANCELED, DirectDebitStatus.PENDING_FOR_CANCELLATION));
    }

    private Map<String, Object> generateFlatObject(String key, Object o) {
        Map<String, Object> output = new LinkedHashMap<>();

        if(o instanceof Map) {
            output.putAll(prepareFlatMap(key, (Map) o));
        } else if(o instanceof List) {
            output.putAll(prepareFlatList(key, (List) o));
        } else if(o instanceof ClientMgmtPaymentDetailsDto) {
            for(Field f : ClientMgmtPaymentDetailsDto.class.getDeclaredFields()) {
                f.setAccessible(true);
                if(f.isAnnotationPresent(JsonIgnore.class)) continue;

                try {
                    output.put(f.getName(), f.get(o));
                } catch (IllegalAccessException e) {
                    logger.log(Level.SEVERE.SEVERE, "Exception in 'generateFlatObject' for field: '" +
                            f.getName() + "'");
                    throw new RuntimeException(e);
                }
            }
        }

        return output;
    }

    private Map<String, Object> prepareFlatMap(String rootKey, Map<String, Object> m) {
        Map<String, Object> output = new LinkedHashMap<>();

        for(String k : m.keySet()) {
            String newKey = (rootKey.isEmpty() ? "" : rootKey + "_") + k;

            if(m.get(k) instanceof Map || m.get(k) instanceof List ||
                    m.get(k) instanceof ClientMgmtPaymentDetailsDto) {

                output.putAll(generateFlatObject(newKey, m.get(k)));
            } else {
                output.put(newKey, m.get(k));
            }

        }

        return output;
    }

    private Map<String, Object> prepareFlatList(String rootKey, List<Object> l) {
        Map<String, Object> output = new LinkedHashMap<>();

        int i = 1;
        for(Object o : l) {
            if(o instanceof Map || o instanceof List) {
                Map<String, Object> m = generateFlatObject(rootKey, o);

                for (String k : m.keySet()) {
                    output.put(k + "_" + i, m.get(k));
                }
            } else if (o instanceof ClientMgmtPaymentDetailsDto) {
                Map<String, Object> m = generateFlatObject(rootKey, o);

                for (String k : m.keySet()) {
                    output.put(StringUtils.toCamelCase(((ClientMgmtPaymentDetailsDto) o).getPaymentCode()) + "_" + k, m.get(k));
                }
            } else {
                output.put(rootKey + "_" + i, o);
            }

            i++;
        }

        return output;
    }

    public Map<String, Object> prepareFlatResponse(Map<String, Object> input) {
        return prepareFlatMap("", input);
    }
}