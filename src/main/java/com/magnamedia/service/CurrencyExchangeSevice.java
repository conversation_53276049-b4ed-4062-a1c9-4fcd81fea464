package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @date 1/31/2021
 * ACC-3053
 */
@Service
public class CurrencyExchangeSevice {
    private static final Logger logger = Logger.getLogger(CurrencyExchangeSevice.class.getName());

    public Double exchange(PicklistItem from, PicklistItem to, Double value) {
        logger.log(Level.SEVERE, "convert currency from " + from.getCode() + " to " + to.getCode());
        from = Setup.getRepository(PicklistItemRepository.class).findOne(from.getId());
        to = Setup.getRepository(PicklistItemRepository.class).findOne(to.getId());
        if (from.hasTag(to.getCode())) {
            String rate = from.getTagValue(to.getCode()).getValue();
            MathContext mathContext = new MathContext(3, RoundingMode.HALF_UP);
            return BigDecimal.valueOf(value)
                    .multiply(new BigDecimal(rate), mathContext).doubleValue();
        } else {
            throw new RuntimeException(from.getCode() + " has no exchange rate to" + to.getCode());
        }
    }

    public Double exchangeFromLocal(PicklistItem to, Double value) {
        PicklistItem from = getLocalCurrency();
        return exchange(from, to, value);
    }

    public Double exchangeToLocal(PicklistItem from, Double value) {
        PicklistItem to = getLocalCurrency();
        return exchange(from, to, value);
    }

    public PicklistItem getLocalCurrency() {
        String parameter = Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.EXPENSE_LOCAL_CURRENCY);
        PicklistItem localCurrency = PicklistHelper.getItemNoException(
                AccountingModule.EXPENSE_CURRENCY, parameter);
        if (localCurrency != null) return localCurrency;

        throw new RuntimeException("Can not find local currency in picklistItmes");

    }

    public PicklistItem getSpecificCurrency(String currency) {
        PicklistItem currencyItem = PicklistHelper.getItemNoException(AccountingModule.EXPENSE_CURRENCY, currency);
        if (currencyItem == null) {
            logger.log(Level.SEVERE, "Can not find currency in picklistItmes");
            throw new RuntimeException("Can not find currency in picklistItmes");
        } else {
            return currencyItem;
        }
    }

}
