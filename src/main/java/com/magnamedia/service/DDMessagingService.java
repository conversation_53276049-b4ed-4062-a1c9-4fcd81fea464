package com.magnamedia.service;

import com.magnamedia.controller.CollectionFlowLogController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.*;
import com.magnamedia.core.entity.template.ChannelSpecificSetting;
import com.magnamedia.core.entity.template.TemplateChannelParameter;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.SelectFilter;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.TemplateAllowedParameterRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.repository.template.ChannelSpecificSettingRepository;
import com.magnamedia.core.repository.template.TemplateChannelParameterRepository;
import com.magnamedia.core.type.BackgroundTaskQueues;
import com.magnamedia.core.type.template.ChannelSpecificSettingType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.Utils;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.entity.DDMessaging;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import com.magnamedia.controller.CollectionFlowLogController;
import com.sun.istack.logging.Logger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.LocalDate;
import org.joda.time.LocalTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.IOException;
import java.math.BigInteger;
import java.net.BindException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.logging.Level;
import java.util.stream.Collectors;

@Service
public class DDMessagingService {

    public enum DdMessagingMethod {
        MESSAGE, FORCE_SMS, EMAIL, CLIENT_TODO
    }

    private final Logger logger = Logger.getLogger(DDMessagingService.class);

    @Autowired
    private DDMessagingRepository ddMessagingRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    private TemplateUtil templateUtil;
    @Autowired
    private Utils utils;
    @Autowired
    private AccountingTemplateService accountingTemplateService;
    @Autowired
    private CollectionFlowLogRepository collectionFlowLogRepository;
    @Autowired
    private CollectionFlowLogService collectionFlowLogService;


    public void applyDdMessagingBGT(
            Contract contract,
            DDMessaging ddMessaging,
            DirectDebit directDebit,
            DirectDebitRejectionToDo rejectionTodo) {

        Setup.getApplicationContext().getBean(BackgroundTaskService.class)
                .create(new BackgroundTask.builder(
                        "applyDdMessagingBGT_" + contract.getId(),
                        "accounting",
                        "DDMessagingService",
                        "applyDdMessaging")
                        .withRelatedEntity("DDMessaging", ddMessaging.getId())
                        .withParameters(
                                new Class[]{ Long.class, Long.class, Long.class, Long.class },
                                contract.getId(), ddMessaging.getId(),
                                directDebit != null ? directDebit.getId() : null,
                                rejectionTodo != null ? rejectionTodo.getId() : null)
                        .withQueue(BackgroundTaskQueues.NormalOperationsQueue)
                        .withDelay(5 * 60 * 1000L)
                        .build());
    }

    public void applyDdMessaging(
            Long contractId,
            Long ddMessagingId,
            Long directDebitId,
            Long rejectionTodoId) {

        Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
        DDMessaging ddMessaging = Setup.getRepository(DDMessagingRepository.class).findOne(ddMessagingId);
        DirectDebit directDebit = null;
        DirectDebitRejectionToDo rejectionTodo = null;

        if (directDebitId != null) {
            directDebit = Setup.getRepository(DirectDebitRepository.class).findOne(directDebitId);

        }

        if (rejectionTodoId != null) {
            rejectionTodo = Setup.getRepository(DirectDebitRejectionToDoRepository.class).findOne(rejectionTodoId);
        }

        ContractPaymentTerm cpt = null;

        try {
            cpt = contract.getActiveContractPaymentTerm();
        } catch (Exception e) {
            logger.log(Level.SEVERE, "ClientDidNotSignedMessagesBR contract has no active cpt");
            if (contract.getContractPaymentTerms() != null && contract.getContractPaymentTerms().size() > 0)
                cpt = contract.getContractPaymentTerms().get(0);
            else {
                logger.log(Level.SEVERE, "ClientDidNotSignedMessagesBR contract has no cpt");
                return;
            }
        }

        DDMessagingContract ddMessagingContract = getDdMessagingContract(ddMessaging, cpt.getBank());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("ownerType", "DirectDebit");
        parameters.put("ownerId", directDebit.getId().toString());
        parameters.put("directDebitId", directDebit.getId().toString());
        if (contract.getReasonOfTerminationList() != null) {
            parameters.put("contractCancellationReason", contract.getReasonOfTerminationList().getCode());
        }

        parameters.put("relatedEntityType2", rejectionTodo != null ?
                rejectionTodo.getEntityType() : directDebit.getEntityType());
        parameters.put("relatedEntityId2", rejectionTodo != null ?
                rejectionTodo.getId().toString() : directDebit.getId().toString());

        if (directDebit.isCausedTermination()) {
            parameters.put("scheduled_termination_date",
                    contract.getScheduledDateOfTermination());
            parameters.put("scheduled_termination_date - 1 day",
                    new LocalDate(contract.getScheduledDateOfTermination()).minusDays(1).toDate());
        }

        if (!parameters.containsKey("scheduled_termination_date") &&
                rejectionTodo != null &&
                rejectionTodo.isCausedTermination()) {

            parameters.put("scheduled_termination_date",
                    contract.getScheduledDateOfTermination());
            parameters.put("scheduled_termination_date - 1 day",
                    new LocalDate(contract.getScheduledDateOfTermination()).minusDays(1).toDate());
        }
        /*if (ddMessagingContract.getDdMessaging().getCreateHumanSms()) {
            logger.log(Level.SEVERE, "execute creating human sms");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.HUMAN_SMS, parameters);
        }*/

        if (ddMessagingContract.getDdMessaging().isCreateClientToDo()) {
            logger.log(Level.INFO, "creating client todo");
            applyDdMessaging(ddMessaging, null, cpt, DdMessagingMethod.CLIENT_TODO, parameters);
        }

        if (ddMessagingContract.getSendToClient() || ddMessagingContract.getSendToMaid()) {

            logger.log(Level.SEVERE, "execute creating sms");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.MESSAGE, parameters);
        }

        if (ddMessagingContract.getSendAsEmail()) {
            logger.log(Level.SEVERE, "execute sending mail");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.EMAIL, parameters);
        }
    }


    // RELATED TO BOUNCED FLOW
    public void applyDdMessaging(
            Contract contract,
            DDMessaging ddMessaging,
            Payment payment) {

        ContractPaymentTerm cpt;

        try {
            cpt = contract.getActiveContractPaymentTerm();
        } catch (Exception e) {
            logger.info("contract has no active cpt");
            if (contract.getContractPaymentTerms() != null && !contract.getContractPaymentTerms().isEmpty())
                cpt = contract.getContractPaymentTerms().get(0);
            else {
                logger.log(Level.SEVERE, "contract has no cpt");
                return;
            }
        }

        DDMessagingContract ddMessagingContract = getDdMessagingContract(ddMessaging, cpt.getBank());
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("bouncedPaymentId", payment.getId().toString());
        parameters.put("relatedEntityType2", payment.getEntityType());
        parameters.put("relatedEntityId2", payment.getId().toString());

        if (payment.getContractCancellationReason() != null) {
            parameters.put("contractCancellationReason", payment.getContractCancellationReason());
        }

        /*if (ddMessagingContract.getDdMessaging().getCreateHumanSms()) {
            logger.log(Level.INFO, "creating human sms");
            DdMessagingMethod type = ddMessagingContract.getDdMessaging().getEvent().equals(DDMessagingType.BouncedPayment) ?
                    DdMessagingMethod.EXPERT_TODO : DdMessagingMethod.HUMAN_SMS;
            applyDdMessaging(ddMessagingContract, null, cpt, type, parameters);
        }*/

        if (ddMessagingContract.getDdMessaging().isCreateClientToDo()) {
            logger.log(Level.INFO, "creating client todo");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.CLIENT_TODO, parameters);
        }

        if (ddMessagingContract.getSendToClient() || ddMessagingContract.getSendToMaid() ||
                ddMessagingContract.getSendToMaidWhenRetractCancellation()) {

            logger.log(Level.INFO, "creating sms");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.MESSAGE, parameters);
        }

        if (ddMessagingContract.getSendAsEmail()) {
            logger.log(Level.INFO, "sending mail");
            applyDdMessaging(ddMessagingContract, null, cpt, DdMessagingMethod.EMAIL, parameters);
        }
    }

    // RELATED TO FLOW PROCESSOR
    public void applyDdMessaging(
            DDMessaging ddMessaging,
            FlowProcessorEntity entity,
            DdMessagingMethod type,
            Date scheduledDateOfTermination,
            Map<String, Object> m) {

        Map<String, Object> parameters = new HashMap<>();
        if (scheduledDateOfTermination != null) {
            parameters.put("scheduled_termination_date", scheduledDateOfTermination);
            parameters.put("scheduled_termination_date - 1 day",
                    new LocalDate(scheduledDateOfTermination).minusDays(1).toDate());
        }

        if (entity.getDirectDebit() != null) {
            parameters.put("directDebitId", entity.getDirectDebit().getId().toString());
        }

         if (m.containsKey("contractCancellationReason")) {
            parameters.put("contractCancellationReason", m.get("contractCancellationReason"));
         }

        logger.log(Level.SEVERE, "sending message");
        parameters.put("relatedEntityType2", entity.getEntityType());
        parameters.put("relatedEntityId2", entity.getId().toString());
        ContractPaymentTerm cpt = entity.getContract().getActiveContractPaymentTerm();
        applyDdMessaging(getDdMessagingContract(ddMessaging, cpt.getBank()), entity, cpt, type, parameters);

        updateCollectionLogUponSendingNewMessage(entity);
    }

    // GENERIC METHOD
    private void applyDdMessaging(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            DdMessagingMethod type,
            Map<String, Object> parameters) {

        switch(type) {
            case MESSAGE:
                flowProcessorMessagingService.handleDdMessagingSendTime(ddMessagingContract.getDdMessaging(), cpt.getContract().getClient());

                // ACC-6973
                boolean sendTimePassed = (ddMessagingContract.getDdMessaging().getSendTime() != null &&
                        new LocalTime(ddMessagingContract.getDdMessaging().getSendTime().getTime()).isBefore(new LocalTime()) &&
                        ddMessagingContract.getDdMessaging().getSendDate() == null);

                if (ddMessagingContract.getDdMessaging().getSendTime() == null || sendTimePassed) {
                    logger.log(Level.INFO, "createToDo sms for send");
                    applyDdMessagingImmediately(ddMessagingContract, entity, cpt, type, parameters);
                } else {
                    logger.log(Level.INFO, "createToDo sms scheduled");
                    Setup.getApplicationContext().getBean(DdMessagingTodoService.class)
                            .createMessagingTodo(ddMessagingContract, entity, cpt, type, parameters);
                }
                break;
            /*case HUMAN_SMS:
                if (ddMessagingContract.getDdMessaging().getHumanSmsTime() == null) {
                    logger.log(Level.INFO, "createToDo human sms for send");
                    applyDdMessagingImmediately(ddMessagingContract, entity, cpt, type, parameters);
                } else {
                    logger.log(Level.INFO, "createToDo human sms scheduled");
                    Setup.getApplicationContext().getBean(DdMessagingTodoService.class)
                            .createMessagingTodo(ddMessagingContract, entity, cpt, type, parameters);
                }
                break;
            case EXPERT_TODO:
                if (ddMessagingContract.getDdMessaging().getHumanSmsTime() == null) {
                    logger.log(Level.INFO, "create expert todo");
                    applyDdMessagingImmediately(ddMessagingContract, entity, cpt, type, parameters);
                } else {
                    logger.log(Level.INFO, "create expert todo scheduled");
                    Setup.getApplicationContext().getBean(DdMessagingTodoService.class)
                            .createMessagingTodo(ddMessagingContract, entity, cpt, type, parameters);
                }
                break;*/
            case CLIENT_TODO:
                logger.log(Level.INFO, "create client todo");
                applyDdMessagingImmediately(ddMessagingContract, entity, cpt, type, parameters);
                break;
            case EMAIL:
                sendDDMessageEmail(ddMessagingContract, entity, cpt, parameters);
                break;
        }
    }

    // METHOD TO APPLY DDMESSAGING IMMEDIATELY
    private void applyDdMessagingImmediately(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            DdMessagingMethod type,
            Map<String, Object> map) {

        Contract contract = cpt.getContract();
        if (contract == null) return;

        logger.log(Level.INFO, "DDMessagingToDoController execute send");

        Map<String, String> parameters = map.entrySet().stream()
                .filter(m -> m.getKey() != null && m.getValue() !=null)
                .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                    if (e.getValue() instanceof Date)
                        return  DateUtil.formatClientFullDate(e.getValue());
                    else return  (String) e.getValue();
                }));

        switch(type) {
            case MESSAGE:
                flowProcessorMessagingService.fillParameters(ddMessagingContract, entity, cpt, parameters, ddMessagingContract.getSendToClient());
                flowProcessorMessagingService.sendDdMessage(
                        ddMessagingContract, cpt, parameters, new HashMap<>(), null);
                break;
            /*case HUMAN_SMS:
                flowProcessorMessagingService.createDdMessagingHumanSms(
                        ddMessagingContract.getDdMessaging(), cpt, parameters);
                break;
            case EXPERT_TODO:
                flowProcessorMessagingService.createDdMessagingExpertTodo(ddMessagingContract.getDdMessaging(), cpt);
                break;*/
            case CLIENT_TODO:
                flowProcessorMessagingService.createDdMessagingClientTodo(ddMessagingContract, entity, cpt, parameters);
                break;
        }
    }

    @Transactional
    public void updateNotificationsAcc4689ReadSheetData(XSSFSheet sheet) {
        if (sheet != null) {
            logger.log(Level.SEVERE, "Sheet Name = {0}", sheet.getSheetName());
            logger.log(Level.SEVERE, "last row number = {0}", sheet.getLastRowNum());

            for (Row row : sheet) {
                if (row.getRowNum() == 0)
                    continue;
                try {
                    logger.log(Level.SEVERE, "Row Num = {0}", row.getRowNum());

                    String notificationTemplateCode = row.getCell(0).getStringCellValue().trim();
                    logger.log(Level.SEVERE, "Cell 0 notificationTemplateCode = {0}",
                            notificationTemplateCode);

                    if (notificationTemplateCode.isEmpty())
                        break;

                    Template notificationTemplate = templateRepository.findByNameIgnoreCase(notificationTemplateCode);
                    if (notificationTemplate == null) {
                        logger.log(Level.SEVERE, "Error notification template code " + notificationTemplateCode + " not found");
                        continue;
                    }

                    int priority = (int) row.getCell(2).getNumericCellValue();
                    logger.log(Level.SEVERE, "Cell 2 = {0}", priority);
                    ClientMessagePriority clientMessagePriority = ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS;
                    switch (priority) {
                        case 1:
                            notificationTemplate.setNotificationHoursBeforeSendSms(0);
                            notificationTemplate.setNotificationAlwaysSendSms(true);
                            clientMessagePriority = ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION;
                            break;
                        case 2:
                            notificationTemplate.setNotificationHoursBeforeSendSms(2);
                            notificationTemplate.setNotificationAlwaysSendSms(false);
                            notificationTemplate.setSendSMSIfNotReceived(true);
                            break;
                        case 3:
                            notificationTemplate.setNotificationHoursBeforeSendSms(null);
                            notificationTemplate.setNotificationAlwaysSendSms(false);
                            notificationTemplate.setSendSMSIfNotReceived(false);
                            clientMessagePriority = ClientMessagePriority.SEND_SMS_NEXT_DAY;
                            break;
                        case 4:
                            notificationTemplate.setNotificationHoursBeforeSendSms(null);
                            notificationTemplate.setNotificationAlwaysSendSms(false);
                            notificationTemplate.setSendSMSIfNotReceived(false);
                            clientMessagePriority = ClientMessagePriority.DO_NOT_SEND_SMS;
                            break;
                    }
                    DDMessaging ddMessaging = ddMessagingRepository.findByClientTemplate(notificationTemplate);
                    if (ddMessaging != null) {
                        logger.log(Level.SEVERE, "ddMessaging id " + ddMessaging.getId());
                        ddMessaging.setClientMessagePriority(clientMessagePriority);
                        ddMessagingRepository.save(ddMessaging);
                        logger.log(Level.SEVERE, "ddMessaging = {0} saved successfully", ddMessaging.getId());
                    } else logger.log(Level.SEVERE, "ddMessaging not found");

                    Setup.getApplicationContext().getBean(TemplateUtil.class)
                            .updateTemplate(notificationTemplate, new HashMap<>());
                    logger.log(Level.SEVERE, "Template Code = {0} end", notificationTemplate);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Transactional
    public void createDDMessagingACC4837(
            String clientMessage,
            String trials,
            String reminders,
            DirectDebitRejectCategory directDebitRejectCategory,
            boolean cc) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setSendToClient(true);
        ddMessaging.setClientMessage(clientMessage);
        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setTrials(trials);
        ddMessaging.setReminders(reminders);
        ddMessaging.setEvent(DDMessagingType.DirectDebitRejected);
        ddMessaging.setRejectCategory(directDebitRejectCategory);
        ddMessaging.setContractProspectTypes(cc ? "maids.cc_prospect" : "maidvisa.ae_prospect");
        ddMessaging.setDdCategory("A,B");

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }


    public void processMigrationACC4837(List<DDMessaging> ddMessagingList,
                                        DirectDebitRejectCategory directDebitRejectCategory){

        List<DDMessaging> ccDDMessagingList = ddMessagingList.stream()
                .filter(d -> d.getContractProspectTypes().equals("maids.cc_prospect")
                        && d.getRejectCategory().equals(directDebitRejectCategory))
                .collect(Collectors.toList());

        List<DDMessaging> mvDDMessagingList = ddMessagingList.stream()
                .filter(d -> d.getContractProspectTypes().equals("maidvisa.ae_prospect")
                        && d.getRejectCategory().equals(directDebitRejectCategory))
                .collect(Collectors.toList());

        updateDDMessagingACC4837(ccDDMessagingList, true, directDebitRejectCategory);
        updateDDMessagingACC4837(mvDDMessagingList, false, directDebitRejectCategory);
    }

    private void updateDDMessagingACC4837(
            List<DDMessaging> ddMessagingList,
            boolean cc,
            DirectDebitRejectCategory directDebitRejectCategory){

        HashSet<String> s = new HashSet<>();

        // map trial to know if there is reminder == 3 in this trial
        ddMessagingList.stream()
                .filter(d -> d.getReminders().equals("3"))
                .forEach(d -> s.add(d.getTrials()));

        for (DDMessaging ddMessaging : ddMessagingList) {
            ClientMessagePriority clientMessagePriority = ddMessaging.getReminders().equals("0") ?
                    ClientMessagePriority.DO_NOT_SEND_SMS :
                    ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS;

            String smsClientMessage = null;
            String notificationClientMessage = null;
            String reminder2 = null;
            String notificationReminder2 = null;
            String maidMessage = null;

            switch (directDebitRejectCategory) {
                case EID:
                    switch (ddMessaging.getTrials()) {
                        case "1":
                        case "2":
                        case "3":
                            switch (ddMessaging.getReminders()) {
                                case "2":
                                    reminder2 = cc ?
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, " +
                                                    "our system will automatically cancel your service and notify your maid to come back to our accommodation. " +
                                                    "To continue your service, please click on the following payment link @link_send_dd_details@ and send us the correct account holder Emirates ID." :
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, " +
                                                    "our system will automatically cancel your service and your maid's visa. To continue your service, " +
                                                    "please @link_send_dd_details@ and send us the correct account holder Emirates ID.";
                                    notificationReminder2 = cc ? "We're sure your maid is very valuable to you and your family. " +
                                            "Unfortunately, if you don't complete the Bank Payment Form today, our system will " +
                                            "automatically cancel your service and notify your maid to come back to our accommodation. " +
                                            "To continue your service, please @link_send_dd_details_click_here@ and send us the correct account holder Emirates ID." :
                                            "We're sure your maid is very valuable to you and your family. " +
                                                    "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                                                    "To continue your service, please @link_send_dd_details_click_here@ and send us the correct account holder Emirates ID.";
                                    if (!s.contains(ddMessaging.getTrials())) {
                                        ddMessaging.setReminders("3");

                                        String key = ddMessaging.getTrials() + ",2";
                                        if (!s.contains(key)) {
                                            createDDMessagingACC4837(reminder2, ddMessaging.getTrials(),
                                                    "2", DirectDebitRejectCategory.EID, cc);
                                            s.add(key);
                                        }

                                    } else {
                                        smsClientMessage = reminder2;
                                        notificationClientMessage = notificationReminder2;
                                    }
                                    break;
                            }
                            break;
                        case "4":
                            if (cc) {
                                if (ddMessaging.getScheduleTermCategory()
                                        .equals(DirectDebitMessagingScheduleTermCategory.GToday)) {

                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to a mistake in the Emirates ID that you've uploaded. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave.";
                                } else {
                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to a mistake in the Emirates ID that you've uploaded. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave.";
                                }
                            } else {
                                smsClientMessage = "Sadly, our system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                        "We’ve sent your maid an SMS with a link to sign her visa cancellation paper. " +
                                        "Please ask your maid to sign her visa cancellation paper today.";
                            }
                    }
                    break;
                case Signature:
                    switch (ddMessaging.getTrials()) {
                        case "1":
                        case "2":
                            switch (ddMessaging.getReminders()) {
                                case "2":
                                    reminder2 = cc ?
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, " +
                                                    "our system will automatically cancel your service and notify your maid to come back to our accommodation. To continue your service, please click on the following link and sign: @link_send_dd_details@" :
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically " +
                                                    "cancel your service and your maid's visa. To continue your service, please click on the following link and sign: @link_send_dd_details@";
                                    notificationReminder2 = cc ? "We're sure your maid is very valuable to you and your family. " +
                                            "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and notify your maid to come back to our accommodation. " +
                                            "To continue your service, please @link_send_dd_details_click_here@ and sign." :
                                            "We're sure your maid is very valuable to you and your family. " +
                                                    "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                                                    "To continue your service, please @link_send_dd_details_click_here@ and sign.";
                                    if (!s.contains(ddMessaging.getTrials())) {
                                        ddMessaging.setReminders("3");

                                        String key = ddMessaging.getTrials() + ",2";
                                        if (!s.contains(key)) {
                                            createDDMessagingACC4837(
                                                    reminder2, ddMessaging.getTrials(), "2", DirectDebitRejectCategory.Signature, cc);
                                            s.add(key);
                                        }

                                    } else {
                                        smsClientMessage = reminder2;
                                        notificationClientMessage = notificationReminder2;
                                    }
                                    break;
                            }
                            break;
                        case "3":
                        case "4":
                        case "5":
                            switch (ddMessaging.getReminders()) {
                                case "2":
                                    reminder2 = cc ?
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, " +
                                                    "our system will automatically cancel your service and notify your maid to come back to our accommodation. To continue your service. Please, would you take a moment " +
                                                    "to sign again right now on a piece of white paper and send us a photo of the paper by clicking on this link: @link_send_dd_details@ ." :
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. Please, would you take a moment to " +
                                                    "sign again right now on a piece of white paper and send us a photo of the paper by clicking on this link: @link_send_dd_details@ .";
                                    notificationReminder2 = cc ? "We're sure your maid is very valuable to you and your family. " +
                                            "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and notify your maid to come back to our accommodation. To continue your service. " +
                                            "Please, would you take a moment to sign again right now on a piece of white paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ ." :
                                            "We're sure your maid is very valuable to you and your family. " +
                                                    "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                                                    "Please, would you take a moment to sign again right now on a piece of white paper and send us a photo of the paper by @link_send_dd_details_clicking_here@ .";
                                    if (!s.contains(ddMessaging.getTrials())) {
                                        ddMessaging.setReminders("3");

                                        String key = ddMessaging.getTrials() + ",2";
                                        if (!s.contains(key)) {
                                            createDDMessagingACC4837(
                                                    reminder2, ddMessaging.getTrials(), "2", DirectDebitRejectCategory.Signature, cc);
                                            s.add(key);
                                        }

                                    } else {
                                        smsClientMessage = reminder2;
                                        notificationClientMessage = notificationReminder2;
                                    }
                                    break;
                            }
                            break;
                        case "6":
                            if (cc) {
                                if (ddMessaging.getScheduleTermCategory()
                                        .equals(DirectDebitMessagingScheduleTermCategory.GToday)) {

                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to an incorrect signature. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave. ";
                                } else {
                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to an incorrect signature. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave. ";
                                }
                            } else {
                                smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to an incorrect signature. " +
                                        "Since this was the last attempt, our system has automatically cancelled your service. " +
                                        "We’ve sent your maid an SMS with a link to sign her visa cancellation paper. " +
                                        "Please ask your maid to sign her visa cancellation paper today.";
                            }
                            break;
                    }
                    break;
                case Account:
                    switch (ddMessaging.getTrials()) {
                        case "1":
                        case "2":
                        case "3":
                            switch (ddMessaging.getReminders()) {
                                case "2":
                                    reminder2 = cc ?
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, " +
                                                    "our system will automatically cancel your service and notify your maid to come back to our accommodation. To continue your service, please click on the following payment link @link_send_dd_details@ and send us the correct account holder name." :
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. To continue your service, " +
                                                    "please click on the following payment link @link_send_dd_details@ and send us the correct account holder name.";
                                    notificationReminder2 = cc ? "We're sure your maid is very valuable to you and your family. " +
                                            "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and notify your maid to come back to our accommodation. " +
                                            "To continue your service, please @link_send_dd_details_click_here@ and send us the correct account holder name." :
                                            "We're sure your maid is very valuable to you and your family. " +
                                                    "Unfortunately, if you don't complete the Bank Payment Form today, our system will automatically cancel your service and your maid's visa. " +
                                                    "To continue your service, please @link_send_dd_details_click_here@ and send us the correct account holder name.";
                                    if (!s.contains(ddMessaging.getTrials())) {
                                        ddMessaging.setReminders("3");

                                        String key = ddMessaging.getTrials() + ",2";
                                        if (!s.contains(key)) {
                                            createDDMessagingACC4837(
                                                    reminder2, ddMessaging.getTrials(), "2", DirectDebitRejectCategory.Account, cc);
                                            s.add(key);
                                        }

                                    } else {
                                        smsClientMessage = reminder2;
                                        notificationClientMessage = notificationReminder2;
                                    }
                                    break;
                            }
                            break;
                        case "4":
                            if (cc) {
                                clientMessagePriority = ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION;

                                if (ddMessaging.getScheduleTermCategory().equals(
                                        DirectDebitMessagingScheduleTermCategory.GToday)) {
                                    smsClientMessage = "Sadly, our system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                            "We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home. Please allow her to leave.";
                                } else {
                                    smsClientMessage = "Sadly, our system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                            "We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home. Please allow her to leave.";
                                }
                                maidMessage = "@maid_first_name@, Your client's contract with maids.cc has been cancelled. We’ll order a taxi to pick you up on @scheduled_termination_date@ at 7pm and return you to the accommodation. Do not leave before we send you the taxi.";
                            } else {
                                maidMessage = "@maid_name@, Sir and Madam will not pay for your visa anymore so we have to cancel your visa. Please click here and sign your visa cancellation paper today: @visa_cancellation_paper_link@";
                                smsClientMessage = "Sadly, the bank couldn't deduct your payment again due to an incorrect signature. Since this was the last attempt, our system has automatically cancelled your service. " +
                                        "We’ve sent your maid an SMS with a link to sign her visa cancellation paper. Please ask your maid to sign her visa cancellation paper today.";
                            }
                            break;
                    }
                    break;
                case Invalid_Account:
                    switch (ddMessaging.getTrials()) {
                        case "1":
                        case "2":
                        case "3":
                            switch (ddMessaging.getReminders()) {
                                case "2":
                                    reminder2 = cc ?
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form, " +
                                                    "our system will automatically cancel your service. To continue your service, please click on the following payment link @link_send_dd_details@ and send us a photo of a different bank account that's set in AED." :
                                            "We're sure your maid is very valuable to you and your family. Unfortunately, if you don't complete the Bank Payment Form, " +
                                                    "our system will automatically cancel your service and your maid's visa. To continue your service, please click on the following payment link @link_send_dd_details@ and send us a photo of a different bank account that's set in AED.";
                                    notificationReminder2 = cc ? "We're sure your maid is very valuable to you and your family. " +
                                            "Unfortunately, if you don't complete the Bank Payment Form, our system will automatically cancel your service. " +
                                            "To continue your service, please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED." :
                                            "We're sure your maid is very valuable to you and your family. " +
                                                    "Unfortunately, if you don't complete the Bank Payment Form, our system will automatically cancel your service and your maid's visa. " +
                                                    "To continue your service, please @link_send_dd_details_click_here@ and send us a photo of a different bank account that's set in AED.";
                                    if (!s.contains(ddMessaging.getTrials())) {
                                        ddMessaging.setReminders("3");

                                        String key = ddMessaging.getTrials() + ",2";
                                        if (!s.contains(key)) {
                                            createDDMessagingACC4837(
                                                    reminder2, ddMessaging.getTrials(), "2", DirectDebitRejectCategory.Invalid_Account, cc);
                                            s.add(key);
                                        }
                                    } else {
                                        smsClientMessage = reminder2;
                                        notificationClientMessage = notificationReminder2;
                                    }
                                    break;
                            }
                            break;
                        case "4":
                            if (cc) {
                                if (ddMessaging.getScheduleTermCategory().equals(
                                        DirectDebitMessagingScheduleTermCategory.GToday)) {

                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again because the currency of your account is not in AED. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave.";
                                } else {
                                    smsClientMessage = "Sadly, the bank couldn't deduct your payment again because the currency of your account is not in AED. " +
                                            "Since this was the last attempt, our system has automatically cancelled your service. " +
                                            "We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home. " +
                                            "Please allow her to leave.";
                                }
                            } else {
                                smsClientMessage = "Sadly, the bank couldn't deduct your payment again because the currency of your account is not in AED. " +
                                        "Since this was the last attempt, our system has automatically cancelled your service. " +
                                        "We’ve sent your maid an SMS with a link to sign her visa cancellation paper. " +
                                        "Please ask your maid to sign her visa cancellation paper today.";
                            }
                            break;
                    }
                    break;
            }

            boolean updateClientMsg = ddMessaging.getReminders().equals("3");
            if (ddMessaging.getReminders().equals("3") ||
                    ((ddMessaging.getTrials().equals("4") &&
                            !ddMessaging.getRejectCategory().equals(DirectDebitRejectCategory.Signature)) ||
                            (ddMessaging.getTrials().equals("6") &&
                                    ddMessaging.getRejectCategory().equals(DirectDebitRejectCategory.Signature)))) {

                clientMessagePriority = ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION;

                if (cc) {
                    if (ddMessaging.getScheduleTermCategory().equals(
                            DirectDebitMessagingScheduleTermCategory.GToday)) {
                        if (updateClientMsg)
                            smsClientMessage = "Sadly, the system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                    "We'll send an Uber on @scheduled_termination_date@ at 7pm to pick up @maid_first_name@ from your home. Please allow her to leave.";
                        maidMessage = "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                                "We’ll order a taxi to pick you up on @scheduled_termination_date@ at 7 PM and return you to the accommodation. " +
                                "Do not leave before we send you the taxi.";
                    } else {
                        if (updateClientMsg)
                            smsClientMessage = "Sadly, the system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                    "We'll send an Uber today at 7pm to pick up @maid_first_name@ from your home. Please allow her to leave. ";
                        maidMessage = "@maid_first_name@, Your client's contract with maids.cc has been cancelled. " +
                                "We’ll order a taxi to pick you up today at 7 PM and return you to the accommodation. " +
                                "Do not leave before we send you the taxi.";
                    }
                } else {
                    if (updateClientMsg)
                        smsClientMessage = "Sadly, our system has automatically cancelled your service since we still haven't received your Bank Payment Form. " +
                                "We’ve sent your maid an SMS with a link to sign her visa cancellation paper. " +
                                "Please ask your maid to sign her visa cancellation paper today.";
                    maidMessage = "@maid_first_name@, Sir and Madam will not pay for your visa anymore so we have to cancel your visa. " +
                            "Please click here and sign your visa cancellation paper today: @visa_cancellation_paper_link@";
                }
            }

            // update maid template if needed
            if(maidMessage != null){
                ddMessaging.setSendToMaid(true);
                ddMessaging.setMaidMessage(maidMessage);

                if(ddMessaging.getMaidTemplate() != null){
                    Template maidTemplate = templateRepository.findByNameIgnoreCase(
                            ddMessaging.getMaidTemplate().getName());

                    if(maidTemplate != null){
                        maidTemplate.setText(maidMessage);
                        templateUtil.updateTemplate(maidTemplate, new HashMap<>());

                        if (maidTemplate.getNotificationSmsTemplateName() != null) {
                            Template maidTemplateSms = templateRepository.findByNameIgnoreCase(
                                    maidTemplate.getNotificationSmsTemplateName());
                            maidTemplateSms.setText(maidMessage);
                            templateUtil.updateTemplate(maidTemplateSms, new HashMap<>());
                        }
                    }
                }
            }

            if(smsClientMessage != null){
                ddMessaging.setSendToClient(true);
                ddMessaging.setClientMessagePriority(clientMessagePriority);
                ddMessaging.setClientMessage(smsClientMessage);

                Template clientTemplate = templateRepository.findByNameIgnoreCase(
                        ddMessaging.getClientTemplate().getName());
                Template smsTemplate = templateRepository.findByNameIgnoreCase(
                        clientTemplate.getNotificationSmsTemplateName());

                if (smsTemplate != null) {
                    smsTemplate.setText("@greetings@, " + smsClientMessage);
                    templateUtil.updateTemplate(smsTemplate, new HashMap<>());
                }

                if (clientTemplate != null) {
                    if(notificationClientMessage == null){
                        clientTemplate.setText(smsClientMessage);
                    } else {
                        clientTemplate.setText(notificationClientMessage);
                    }
                    templateUtil.updateTemplate(clientTemplate, new HashMap<>());
                }

                if(ddMessaging.getScheduleTermCategory() == null){
                    ddMessaging.setScheduleTermCategory(
                            DirectDebitMessagingScheduleTermCategory.None);
                }
                if (ddMessaging.getDdCategory() == null) {
                    ddMessaging.setDdCategory("A,B");
                }

                ddMessagingRepository.saveAndFlush(ddMessaging);
            }
        }
    }

    @Transactional
    public void createDDMessagingACC4603(int trials){
        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.BouncedPayment);
        ddMessaging.setTrials(Integer.toString(trials));
        ddMessaging.setReminders("0");
        ddMessaging.setContractProspectTypes("maidvisa.ae_prospect");
        ddMessaging.setBouncedPaymentStatus(PicklistHelper.getItem(AccountingModule.PICKLIST_BOUNCED_PAYMENT_STATUS, "has_e-signature_and_manual_dd"));
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        String message = "";
        String smsMessage = "";

        switch (trials){
            case 3:
            case 4:
            case 5:
                message = "We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                        "We'll try again in the next 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please pay using alternative methods by @bounced_payment_clicking_here@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                smsMessage = "@greetings@ We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                        "We'll try again in the next 3 days. Please make sure to add funds to your bank account. If you can't add funds, " +
                        "please click on the following link to pay using alternative methods : @bounced_payment_sms_link@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                //ddMessaging.setCreateHumanSms(trials == 4);
                break;

        }
        if(trials == 3){
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_NEXT_DAY);
        }

        ddMessaging.setSendToMaid(false);
        ddMessaging.setClientMessage(message);
        ddMessaging.setClientSmsMessage(smsMessage);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }


    @Transactional
    public void updateDDMessagingACC4603(
            DDMessaging ddMessaging,
            boolean firstRun) {

        String trials = ddMessaging.getTrials();
        String message = "";
        String smsMessage = null;
        logger.info("start updating old ddMessaging with id: " + ddMessaging.getId());
        logger.info("start updating old ddMessaging with trial: " + ddMessaging.getTrials());
        ddMessaging.setBouncedPaymentStatus(PicklistHelper.getItem(AccountingModule.PICKLIST_BOUNCED_PAYMENT_STATUS, "has_e-signature_and_manual_dd"));
        ddMessaging.setSendToMaid(false);
        switch (trials) {
            case "0":
                message = "Unfortunately, the bank couldn't deduct your payment of AED @bounced_payment_amount@ due to insufficient funds. " +
                        "We'll try deducting the amount again in 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please pay using alternative methods by @bounced_payment_clicking_here@";
                smsMessage = "@greetings@ Unfortunately, the bank couldn't deduct your payment of AED @bounced_payment_amount@ due to insufficient funds. " +
                        "We'll try deducting the amount again in 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please click on the following link to pay using alternative methods: @bounced_payment_sms_link@";
                break;
            case "1":
                message = "Unfortunately, the bank couldn't deduct your payment of AED @bounced_payment_amount@ for the second time due to insufficient funds. " +
                        "We'll try deducting the amount again in 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please pay using alternative methods by @bounced_payment_clicking_here@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                smsMessage = "@greetings@ Unfortunately, the bank couldn't deduct your payment of AED @bounced_payment_amount@ for the second time " +
                        "due to insufficient funds. We'll try deducting the amount again in 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please click on the following link to pay using alternative methods : @bounced_payment_sms_link@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa. ";
                break;
            case "2":
            case "4":
            case "5":
                message = "We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                        "We'll try again in the next 3 days. Please make sure to add funds to your bank account. " +
                        "If you can't add funds, please pay using alternative methods by @bounced_payment_clicking_here@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                smsMessage = "@greetings@ We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                        "We'll try again in the next 3 days. Please make sure to add funds to your bank account. If you can't add funds, " +
                        "please click on the following link to pay using alternative methods : @bounced_payment_sms_link@ " +
                        "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
                break;
            case "3":
                if (firstRun) {
                    ddMessaging.setTrials("6");
                } else {
                    message = "We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                            "We'll try again in the next 3 days. Please make sure to add funds to your bank account. " +
                            "If you can't add funds, please pay using alternative methods by @bounced_payment_clicking_here@ " +
                            "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                    smsMessage = "@greetings@ We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
                            "We'll try again in the next 3 days. Please make sure to add funds to your bank account. If you can't add funds, " +
                            "please click on the following link to pay using alternative methods : @bounced_payment_sms_link@ " +
                            "If we still can't deduct the amount, we'll have no option but to cancel your service and your maid’s visa.";
                    ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);

                }
                break;
        }

        if (ddMessaging.getTrials().equals("6")) {
//            if(ddMessaging.getScheduleTermCategory().equals(
//                    DirectDebitMessagingScheduleTermCategory.None)){
//
//                message = "@greetings@ We've tried deducting your payment several times, but we couldn't as your account has insufficient funds. " +
//                        "We'll try again in the next 3 days. Please make sure to add funds to your bank account. If you can't add funds, " +
//                        "please click on the following link to pay using alternative methods: @link@ If we still can't deduct the amount, " +
//                        "we'll have no option but to cancel your service and your maid's visa.";
//            } else {
            message = "Our system has automatically cancelled your service since we tried deducting your monthly payment several times but we couldn't due to insufficient funds in your account. Please ask your maid to sign her visa cancellation paper today. Thank you. ";
            smsMessage = message;
            String maidMessage = "@maid_name@, Sir and Madam will not pay for your visa anymore so we have to cancel your visa. " +
                    "Please click here and sign your visa cancellation paper today: @visa_cancellation_paper_link@";
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
            if (ddMessaging.getMaidTemplate() != null) {
                Template maidTemplate = templateRepository.findByNameIgnoreCase(ddMessaging.getMaidTemplate().getName());
                maidTemplate.setText(maidMessage);
                templateUtil.updateTemplate(maidTemplate, new HashMap<>());
            } else {
                ddMessaging.setMaidMessage(maidMessage);
            }
            ddMessaging.setSendToMaid(true);
        }
        if(Arrays.asList("0", "1", "2", "3").contains(ddMessaging.getTrials())){
            ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_NEXT_DAY);
        }
        if(ddMessaging.getScheduleTermCategory() == null){
            ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        }

        logger.info("updating old ddMessaging for trial: " + trials);
        /*if (ddMessaging.getBouncedPaymentStatus() != null
            && ddMessaging.getBouncedPaymentStatus().getCode().equals("has_e-signature_and_manual_dd"))
            ddMessaging.setCreateHumanSms(trials.equals("4"));*/

        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setReminders("0");
        Template clientTemplate = templateRepository.findByNameIgnoreCase(ddMessaging.getClientTemplate().getName());
        clientTemplate.setText(message);
        templateUtil.updateTemplate(clientTemplate, new HashMap<>());
        if (smsMessage != null) {
            Template smsClientTemplate = templateRepository.findByNameIgnoreCase(clientTemplate.getNotificationSmsTemplateName());
            smsClientTemplate.setText(smsMessage);
            templateUtil.updateTemplate(smsClientTemplate, new HashMap<>());
        }
        ddMessagingRepository.saveAndFlush(ddMessaging);
    }


    public void updateNotificationsACC5214ReadSheetData(
            MultipartFile file,
            String sheetName,
            boolean withMaidTemplate,
            String contractProspectTypes) throws IOException {

        TemplateUtil templateUtil = Setup.getApplicationContext().getBean(TemplateUtil.class);
        logger.log(Level.WARNING, "Sheet Name = {0}", sheetName);
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        logger.log(Level.INFO, "last row number = {0}", sheet.getLastRowNum());
        for (Row row : sheet) {

            if (row.getRowNum() == 0)
                continue;

            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());

                long ddMessagingId = (long) row.getCell(0).getNumericCellValue();
                logger.log(Level.INFO, "Cell 0 ddMessagingId = {0}", ddMessagingId);

                if (ddMessagingId == -1) continue;
                if (ddMessagingId == 0) break;

                DDMessaging ddMessaging = ddMessagingRepository.findOne(ddMessagingId);
                if (ddMessaging == null) {
                    logger.log(Level.WARNING, "Error dd message " + ddMessagingId + " not found");
                    continue;
                }

                if (!ddMessaging.getContractProspectTypes().contains(contractProspectTypes)) {
                    logger.log(Level.WARNING, "Error dd contractProspectTypes issue ddMessagingId: {0}; " +
                            "contractProspectTypes: {1}", new Object[]{ddMessagingId, contractProspectTypes});
                    continue;
                }

                Template t = ddMessaging.getClientTemplate();
                t.setText(AddSpaceToLinkWithDotOrComma(row.getCell(1).getStringCellValue().trim()));
                templateUtil.updateTemplate(t, new HashMap<>());

                Template sms = TemplateUtil.getTemplate(t.getNotificationSmsTemplateName());
                sms.setText(AddSpaceToLinkWithDotOrComma(row.getCell(2).getStringCellValue().trim()));
                templateUtil.updateTemplate(sms, new HashMap<>());

                int whenToSendIndex = 4;
                if (withMaidTemplate) {
                    String maidText = row.getCell(3).getStringCellValue().trim();
                    ddMessaging.setSendToMaid(false);
                    if (!maidText.equals("-")) {
                        Template maid = ddMessaging.getMaidTemplate();
                        ddMessaging.setSendToMaid(true);
                        maidText = AddSpaceToLinkWithDotOrComma(maidText);
                        if (maid != null) {
                            maid.setText(maidText);
                            templateUtil.updateTemplate(maid, new HashMap<>());
                            if (maid.getNotificationSmsTemplateName() != null) {
                                Template maidSms = TemplateUtil.getTemplate(maid.getNotificationSmsTemplateName());
                                maidSms.setText(maidText);
                                templateUtil.updateTemplate(maidSms, new HashMap<>());
                            }
                        } else {
                            ddMessaging.setMaidMessage(maidText);
                        }
                    }
                } else whenToSendIndex--;

                int whenToSentSms = (int) row.getCell(whenToSendIndex).getNumericCellValue();
                logger.log(Level.INFO, "Cell " + whenToSendIndex + " = {0}", whenToSentSms);

                switch (whenToSentSms) {
                    case 1:
                        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
                        break;
                    case 2:
                        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
                        break;
                    case 3:
                        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_NEXT_DAY);
                        break;
                    case 4:
                        ddMessaging.setClientMessagePriority(ClientMessagePriority.DO_NOT_SEND_SMS);
                        break;
                }

                ddMessagingRepository.save(ddMessaging);
                logger.log(Level.INFO, "Dd message id = {0} end", ddMessagingId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public String AddSpaceToLinkWithDotOrComma(String text) {

        List<String> parameters = Arrays.asList(
            "@bounced_payment_sms_link@",
            "@bounced_payment_clicking_here@",
            "@payment_bounced_sign_now_clicking_here@",
            "@pay_using_different_bank_account_sms@",
            "@pay_using_different_bank_account_click_here@",

            "@chat_with_us_link@",
            "@chat_with_us@",

            "@link_send_dd_details@",
            "@link_send_dd_details_click_here@",
            "@link_send_dd_details_here@",
            "@link_send_dd_details_clicking_here@",
            "@link_send_dd_details_payment_link@",
            "@sign_now@",
            "@spouse_sing_dd_Link@",
            "@spouse_sing_dd_Link_click_here@",

            "@paytab_link@",
            "@paytab_link_click_here@",
            "@payment_credit_card@",
            "@paytabs_link@",

            "@proof_of_transfer_link_Click_here@",
            "@proof_of_transfer_link@",

            "@visa_cancellation_paper_link@");

        AtomicReference<String> result = new AtomicReference<>();
        result.set(text);
        parameters.forEach(p -> {
            if (text.contains(p + ".") || text.contains(p + ","))
                result.set(result.get().replace(p, p + " "));
        });
        return result.get();
    }

    public String fetchTemplateName(String basic, long index) {
        String k = basic + index;

        while(Setup.getRepository(AccTemplateRepository.class).existsByName(k)) {
            k = basic + (++index);
        }

        return k;
    }

    public void sendDDMessageEmail(
            DDMessagingContract ddMessagingContract,
            FlowProcessorEntity entity,
            ContractPaymentTerm cpt,
            Map<String, Object> map) {

        Map<String, String> parameters = map.entrySet().stream()
                .filter(m -> m.getKey() != null && m.getValue() !=null)
                .collect(Collectors.toMap(Map.Entry::getKey, e -> {
                    if (e.getValue() instanceof Date)
                        return  DateUtil.formatClientFullDate(e.getValue());
                    else return  (String) e.getValue();
                }));

        flowProcessorMessagingService.fillParameters(ddMessagingContract, entity, cpt, parameters, false);

        Setup.getApplicationContext()
                .getBean(MessagingService.class)
                .sendEmailToClient(cpt.getContract(),
                        ddMessagingContract.getClientTemplate().getName(),
                        parameters,
                        cpt.getContract().getClient().getEmail(),
                        ddMessagingContract.getEmailSubject());
    }

    public void updateNotificationsCMA3901ReadSheetData(
            MultipartFile file,
            String sheetName) throws IOException {

        logger.info("Sheet Name: " + sheetName);
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.warning("Sheet not found");
            return;
        }

        logger.info("last row number: " + sheet.getLastRowNum());
        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.info("Row Num: " + row.getRowNum());

                if (row.getCell(0).getStringCellValue() == null ||
                        row.getCell(0).getStringCellValue().isEmpty()) break;

                Template t = TemplateUtil.getTemplate(row.getCell(0).getStringCellValue());

                if (t == null) continue;

                logger.info("t Name: " + t.getName());

                t.setText(row.getCell(1).getStringCellValue());
                templateUtil.updateTemplate(t, new HashMap<>());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public boolean updateNotificationsACC6255ReadSheetDataIPAM(
            MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet("IPAM");

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return false;
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientPaidCashAndNoSignatureProvided);
        query.filterBy("deleted", "=", false);

        List<DDMessaging> l = query.execute();
        logger.log(Level.INFO, "last row number = {0}", sheet.getLastRowNum());
        boolean firstTime = l.stream().mapToInt(d -> Integer.parseInt(d.getTrials())).max().getAsInt() == 9;
        logger.info("firstTime: " + firstTime);

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());

                int trial = (int) row.getCell(0).getNumericCellValue();
                int newTrial = 1;
                logger.log(Level.INFO, "trial: " + trial);
                if (trial == -1) break;

                boolean sendPayTabMessage = row.getCell(4).getNumericCellValue() == 1;
                String contractProspectTypes = row.getCell(6).getStringCellValue();
                logger.info("sendPayTabMessage: " + sendPayTabMessage);
                List<DDMessaging> ddMessagingList = null;
                switch (trial) {
                    case 1:
                        ddMessagingList = l.stream().filter(d -> d.getTrials().equals("1")).collect(Collectors.toList());
                        break;
                    case 2:
                        ddMessagingList = l.stream().filter(d -> d.getTrials().equals((firstTime ? "4" : "2")) &&
                                d.getSendPayTabMessage().equals(sendPayTabMessage)).collect(Collectors.toList());
                        newTrial = 2;
                        break;
                    case 3:
                        ddMessagingList = l.stream().filter(d -> d.getTrials().equals((firstTime ? "5" : "3")) &&
                                d.getSendPayTabMessage().equals(sendPayTabMessage)).collect(Collectors.toList());
                        newTrial = 3;
                        break;
                    case 4:
                        if (firstTime) {
                            ddMessagingList = l.stream().filter(d -> d.getContractProspectTypes().equals(contractProspectTypes) &&
                                    d.getTrials().equals((sendPayTabMessage ? "6" : "2")) &&
                                    d.getSendPayTabMessage().equals(sendPayTabMessage)).collect(Collectors.toList());
                        } else {
                            ddMessagingList = l.stream().filter(d -> d.getContractProspectTypes().equals(contractProspectTypes) &&
                                    d.getTrials().equals(("4")) &&
                                    d.getSendPayTabMessage().equals(sendPayTabMessage)).collect(Collectors.toList());
                        }

                        newTrial = 4;
                        break;
                    case 5:
                        ddMessagingList = l.stream().filter(d -> d.getContractProspectTypes().equals(contractProspectTypes) &&
                                        d.getTrials().equals((firstTime ? "9" : "5")) &&
                                        d.getScheduleTermCategory().equals((
                                                row.getCell(5).getStringCellValue().equals("GTODAY") ?
                                                        DirectDebitMessagingScheduleTermCategory.GToday :
                                                        DirectDebitMessagingScheduleTermCategory.EToday)))
                                .collect(Collectors.toList());
                        newTrial = 5;
                        break;
                }

                if (ddMessagingList != null && !ddMessagingList.isEmpty()) {
                    l.removeAll(ddMessagingList);
                    for (DDMessaging d : ddMessagingList) {
                        logger.info("ddMessaging id: " + d.getId());
                        d.setTrials(String.valueOf(newTrial));
                        d.setActive(true);
                        ddMessagingRepository.save(d);

                        String n = row.getCell(1).getStringCellValue();
                        String s = row.getCell(2).getStringCellValue();
                        String maidText = row.getCell(3).getStringCellValue();
                        if (d.getContractProspectTypes().equals("maidvisa.ae_prospect")) {
                            n = n.replace("paid_end_date", "adjusted_end_date");
                            s = s.replace("paid_end_date", "adjusted_end_date");
                        }
                        Map<String, Object> m = getUpdateTemplateMap(n, s);

                        accountingTemplateService.createOrUpdateNewModelTemplate(d.getClientTemplate().getName(),
                                n, s, 0, m);

                        if (!maidText.equals("-")) {
                            accountingTemplateService.createOrUpdateNewModelTemplate(
                                    d.getMaidTemplate().getName(), maidText, maidText, 0, new HashMap<>());
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (!l.isEmpty()) {
            l.forEach(d -> {
                logger.info("flag as delete, dd messaging: " + d.getId());
                d.setDeleted(true);
                d.setActive(false);
            });

            ddMessagingRepository.save(l);
        }
        return firstTime;
    }

    public void updateNotificationsACC6255ReadSheetDataOCCR(
            MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet("OCCR");

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.OnlineCreditCardPaymentReminders);
        query.filterBy("isActive", "=", true);

        List<DDMessaging> l = query.execute();
        logger.log(Level.INFO, "last row number = {0}", sheet.getLastRowNum());

        l.forEach(d -> d.setDeleted(true));

        Map<String, Object> m = new HashMap<String, Object>() {{
           put("notificationCanClosedByUser", false);
        }};
        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());

                String n = row.getCell(0).getStringCellValue();
                logger.log(Level.INFO, "n = {0}", n);
                if (n == null || n.isEmpty() || n.equals("-1")) break;

                int reminder = (int) row.getCell(2).getNumericCellValue();
                int priority = (int) row.getCell(3).getNumericCellValue();
                logger.log(Level.INFO, "reminder = {0}", reminder);
                List<DDMessaging> ddMessaging = l.stream()
                        .filter(d -> d.getReminders().equals(String.valueOf(reminder)))
                        .collect(Collectors.toList());

                if (!ddMessaging.isEmpty()) {
                    l.removeAll(ddMessaging);
                    ddMessaging.forEach(d -> {

                        accountingTemplateService.createOrUpdateNewModelTemplate(d.getClientTemplate().getName(),
                                n, row.getCell(1).getStringCellValue(), priority, m);
                        d.setDeleted(false);
                        ddMessagingRepository.save(d);
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        ddMessagingRepository.save(l);
    }

    public void updateNotificationsACC6255ReadSheetDataPaymentForApprovalRequest(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet("PAYMENT_FOR_APPROVAL_REQUEST");

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }
        try {
            Row row = sheet.getRow(1);
            String n = row.getCell(0).getStringCellValue();
            int priority = (int) row.getCell(2).getNumericCellValue();
            Map<String, Object> m = new HashMap<String, Object>() {{
                put("notificationCanClosedByUser", false);
                put("contractType", "maids.cc");
            }};
            accountingTemplateService.createOrUpdateNewModelTemplate(
                    CcNotificationTemplateCode.CC_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString(),
                    n,
                    row.getCell(1).getStringCellValue(),
                    priority,
                    m);

            m.put("contractType", "maidvisa");
            accountingTemplateService.createOrUpdateNewModelTemplate(
                    MvNotificationTemplateCode.MV_PAYMENT_FOR_APPROVAL_REQUEST_FROM_ERP.toString(),
                    n,
                    row.getCell(1).getStringCellValue(),
                    priority,
                    m);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void updateNotificationsACC6064ReadSheetData(
            MultipartFile file,
            String sheetName,
            boolean firstTime) throws IOException {

        logger.log(Level.WARNING, "Sheet Name = {0}", sheetName);
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.BouncedPayment);
        query.filterBy("bouncedPaymentStatus.code", "=", "has_e-signature_and_manual_dd");
        query.filterBy("contractProspectTypes", "=", sheetName.equals("cc") ?
                "maids.cc_prospect" : "maidvisa.ae_prospect");

        List<DDMessaging> l = query.execute();
        logger.log(Level.INFO, "last row number = {0}", sheet.getLastRowNum());

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());

                int trial = (int) row.getCell(0).getNumericCellValue();
                logger.log(Level.INFO, "Cell 0 trial = {0}", trial);

                if (trial == -1 || row.getCell(1).getStringCellValue() == null || row.getCell(1).getStringCellValue().isEmpty()) break;
                if (trial == 0) {
                    DDMessaging dd = l.stream()
                            .filter(d -> d.getTrials().equals(String.valueOf(trial)))
                            .findFirst()
                            .orElse(null);

                    if (dd == null) {
                        logger.log(Level.WARNING, "Error dd message trial " + trial + " not found");
                        continue;
                    }

                    updateDDMessagingTemplateACC6064(
                            dd, row.getCell(1).getStringCellValue(), row.getCell(2).getStringCellValue());
                    continue;
                }

                DDMessaging dd;
                if (firstTime) {
                    dd = createDDMessagingACC6064(
                            trial, row.getCell(1).getStringCellValue(),
                            row.getCell(2).getStringCellValue(),
                            sheetName.equals("cc") ? "maids.cc_prospect" : "maidvisa.ae_prospect");
                } else {
                    dd = l.stream()
                            .filter(d -> d.getTrials().equals(String.valueOf(trial)))
                            .findFirst()
                            .orElse(null);

                    if (dd == null) {
                        logger.log(Level.WARNING, "Error dd message trial " + trial + " not found");
                        continue;
                    }

                    updateDDMessagingTemplateACC6064(
                            dd, row.getCell(1).getStringCellValue(), row.getCell(2).getStringCellValue());

                }

                if (dd != null) {
                    dd = ddMessagingRepository.findOne(dd.getId());
                    ChannelSpecificSettingRepository channelSpecificSettingRepository = Setup.getRepository(ChannelSpecificSettingRepository.class);
                    Template t = dd.getClientTemplate();
                    ChannelSpecificSetting s = t.getChannelSetting(ChannelSpecificSettingType.Notification);
                    s = channelSpecificSettingRepository.findOne(s.getId());
                    s.setMessageDelayType(PicklistHelper.getItem("template_message_delay", "no_sms"));
                    channelSpecificSettingRepository.save(s);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (firstTime){
            l.stream()
                    .filter(d -> !d.getTrials().equals("0"))
                    .forEach(d -> d.setTrials(String.valueOf(Integer.parseInt(d.getTrials()) + 1)));
            ddMessagingRepository.saveAll(l);
        }
    }

    public DDMessaging createDDMessagingACC6064(
            int trials, String notification, String sms, String contractType){

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.BouncedPayment);
        ddMessaging.setTrials(Integer.toString(trials));
        ddMessaging.setReminders("0");
        ddMessaging.setContractProspectTypes(contractType);
        ddMessaging.setBouncedPaymentStatus(PicklistHelper.getItem(AccountingModule.PICKLIST_BOUNCED_PAYMENT_STATUS, "has_e-signature_and_manual_dd"));
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setClientMessagePriority(ClientMessagePriority.DO_NOT_SEND_SMS);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setClientMessage(notification);
        ddMessaging.setClientSmsMessage(sms);

        return ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public void updateDDMessagingTemplateACC6064(
            DDMessaging dd, String n, String s){

        Template t = dd.getClientTemplate();
        if (t.isNewModel()) {
            if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
                t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(n);
            }
            if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
                t.getChannelSetting(ChannelSpecificSettingType.SMS).setText(s);
            }
            TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());
            return;
        }

        t.setText(n);
        templateUtil.updateTemplate(t, new HashMap<>());

        Template sms = TemplateUtil.getTemplate(dd.getClientTemplate().getNotificationSmsTemplateName());
        sms.setText(s);
        templateUtil.updateTemplate(sms, new HashMap<>());
    }

    public void updateNotificationsAcc6344ReadSheetData(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet("Sheet1");

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.IncompleteDDClientHasNoApprovedSignature);
        query.filterBy("trials", "=", "1");
        query.filterBy("reminders", "=", "2");
        query.filterBy("contractProspectTypes", "=", "maidvisa.ae_prospect");

        List<DDMessaging> l = query.execute();
        logger.log(Level.INFO, "last row number = {0}", sheet.getLastRowNum());

        if (l.isEmpty()) {
            query = new SelectQuery<>(DDMessaging.class);
            query.filterBy("event", "=", DDMessagingType.IncompleteDDClientHasNoApprovedSignature);
            query.filterBy("trials", "=", "1");
            query.filterBy("reminders", "<", "2");
            query.filterBy("contractProspectTypes", "=", "maids.cc_prospect");

            query.execute().forEach(d -> {
                d.setContractProspectTypes("maidvisa.ae_prospect,maids.cc_prospect");
                ddMessagingRepository.save(d);
            });
        }

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());

                if (row.getCell(0).getStringCellValue() == null ||
                        row.getCell(0).getStringCellValue().isEmpty())
                    break;

                DirectDebitMessagingScheduleTermCategory category = row.getCell(0)
                        .getStringCellValue()
                        .equals("Today") ?
                        DirectDebitMessagingScheduleTermCategory.EToday :
                        DirectDebitMessagingScheduleTermCategory.GToday;
                String n = row.getCell(1).getStringCellValue();
                String sms = row.getCell(2).getStringCellValue();
                String m = row.getCell(3).getStringCellValue();

                DDMessaging dd;
                if (l.isEmpty()) {
                    dd = createDDMessagingAcc6344(n, sms, m, category);
                } else {
                    dd = l.stream()
                            .filter(d -> d.getScheduleTermCategory().equals(category))
                            .findFirst()
                            .orElse(null);

                    if (dd == null) continue;
                    updateDDMessagingTemplateAcc6344(dd, n, sms, m);
                }

                if (dd != null) {
                    dd = ddMessagingRepository.findOne(dd.getId());
                    ChannelSpecificSettingRepository channelSpecificSettingRepository = Setup.getRepository(ChannelSpecificSettingRepository.class);
                    Template t = dd.getClientTemplate();
                    ChannelSpecificSetting s = t.getChannelSetting(ChannelSpecificSettingType.Notification);
                    s = channelSpecificSettingRepository.findOne(s.getId());
                    s.setMessageDelayType(Setup.getItem("template_message_delay", "x_hours_after_notification"));
                    s.setMessageDelayMinutes(2 * 60);
                    channelSpecificSettingRepository.save(s);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void updateDDMessagingTemplateAcc6344(
            DDMessaging dd,
            String n,
            String s,
            String m){

        Template t = dd.getClientTemplate();
        if (t.isNewModel()) {
            if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
                t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(n);
            }
            if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
                t.getChannelSetting(ChannelSpecificSettingType.SMS).setText(s);
            }
            TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());
        }

        t = dd.getMaidTemplate();
        if (t.isNewModel()) {
            if (t.isChannelExist(ChannelSpecificSettingType.Notification)) {
                t.getChannelSetting(ChannelSpecificSettingType.Notification).setText(m);
            }
            if (t.isChannelExist(ChannelSpecificSettingType.SMS)) {
                t.getChannelSetting(ChannelSpecificSettingType.SMS).setText(m);
            }
            TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());
        }
    }

    public DDMessaging createDDMessagingAcc6344(
            String notification,
            String sms,
            String m,
            DirectDebitMessagingScheduleTermCategory scheduleTermCategory) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.IncompleteDDClientHasNoApprovedSignature);
        ddMessaging.setTrials("1");
        ddMessaging.setReminders("2");
        ddMessaging.setContractProspectTypes("maidvisa.ae_prospect");
        ddMessaging.setScheduleTermCategory(scheduleTermCategory);
        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_AFTER_TWO_HOURS);
        ddMessaging.setSendToMaid(true);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setClientMessage(notification);
        ddMessaging.setClientSmsMessage(sms);
        ddMessaging.setMaidMessage(m);

        return ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public Map<String, Object> createMessageTemplateIfNeeded(Map<String, Object> m) {
        m.put("clientTarget", PicklistHelper.getItem("template_target", "Clients"));
        m.put("maidTarget", PicklistHelper.getItem("template_target", "Housemaids"));
        Map<String, Object> r = new HashMap<>();

        if (m.containsKey("contractProspectTypes")) {
            String contractProspectTypes = (String) m.get("contractProspectTypes");
            m.put("contractType",
                    contractProspectTypes.contains("maidvisa.ae_prospect") &&
                            contractProspectTypes.contains("maids.cc_prospect") ? "both" :
                            contractProspectTypes.contains("maids.cc_prospect") ? "maids.cc" : "maidvisa");
        }

        if ((Boolean) m.get("sendToClient")) {
            r.putAll(createClientTemplate(m));
        }

        Template clientTemplate = (Template) m.get("clientTemplate");
        if ((Boolean) m.get("sendAsEmail")) {
            if (r.containsKey("clientTemplate")) {
                m.put("clientTemplate", r.get("clientTemplate"));
            }
            createEmailChannel(m);

        } else if (clientTemplate != null &&
                clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email) != null &&
                clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email).isActive()) {
            ChannelSpecificSetting c = clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email);
            c.setActive(false);
            Setup.getRepository(ChannelSpecificSettingRepository.class).save(c);
        }

        if ((Boolean) m.get("sendToMaid")) {
            r.putAll(createMaidTemplate(m));
        }

        // ACC-2445
        if ((Boolean) m.get("sendToMaidWhenRetractCancellation")) {
            r.putAll(createMaidWhenRetractCancellationTemplate(m));
        }

        return r;
    }

    private Map<String, Object> createClientTemplate(Map<String, Object> m) {
        String k = (String) m.get("notificationTemplateName");
        String clientMessage = (String) m.getOrDefault("clientMessage", "");

        Map<String, Object> r = new HashMap<>();
        Template clientTemplate = (Template) m.get("clientTemplate");
        String smsText = (String) m.get("smsText");
        if (!smsText.contains("@greetings@")) smsText = "@greetings@ " + smsText;

        if (clientTemplate == null) {
            Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(k, clientMessage, smsText, 2,  m);
            r.put("clientTemplate", t);
            logger.info("Client Notification: " + k);

        } else if (clientTemplate.getChannelSetting(ChannelSpecificSettingType.Notification) == null) {
            accountingTemplateService.createOrUpdateNewModelTemplate(k, clientMessage, smsText, 2,  m);
        }

        return r;
    }

    private void createEmailChannel(Map<String, Object> m) {
        Template clientTemplate = (Template) m.get("clientTemplate");

        if (clientTemplate == null) {
            return;
        }

        if (clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email) == null &&
                clientTemplate.isChannelExist(ChannelSpecificSettingType.Notification)) {

            ChannelSpecificSetting setting = new ChannelSpecificSetting();
            setting.setType(ChannelSpecificSettingType.Email);
            setting.setActive(true);
            setting.setText(clientTemplate.getChannelSetting(ChannelSpecificSettingType.SMS).getText());
            setting.setTemplate(clientTemplate);
            setting = Setup.getRepository(ChannelSpecificSettingRepository.class).save(setting);

            clientTemplate.getChannelSpecificSettings().add(setting);
            Setup.getRepository(TemplateRepository.class).save(clientTemplate);

            TemplateChannelParameterRepository templateChannelParameterRepository =
                    Setup.getRepository(TemplateChannelParameterRepository.class);
            for (TemplateChannelParameter p :
                    clientTemplate.getChannelSetting(ChannelSpecificSettingType.SMS)
                            .getParameters()) {
                TemplateChannelParameter tp = new TemplateChannelParameter();
                tp.setName(p.getName());
                tp.setSetting(setting);
                tp.setValue(p.getValue());
                tp.setExpression(p.isExpression());
                templateChannelParameterRepository.saveAndIgnoreDuplication(tp);
            }
        } else if (clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email) != null &&
                !clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email).isActive()) {
            ChannelSpecificSetting c = clientTemplate.getChannelSetting(ChannelSpecificSettingType.Email);
            c.setActive(true);
            Setup.getRepository(ChannelSpecificSettingRepository.class).save(c);
        }
    }

    private Map<String, Object> createMaidTemplate(Map<String, Object> m) {
        String maidMessage = (String) m.get("maidMessage");
        Map<String, Object> r = new HashMap<>();
        Template maidTemplate = (Template) m.get("maidTemplate");

        if (maidTemplate != null) return r;
        String maid_k = (String) m.get("maid_k");

        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(maid_k, maidMessage, maidMessage, 2, new HashMap<String, Object>() {{
            put("target", m.get("maidTarget"));
            if (m.containsKey("contractType")) put("contractType", m.get("contractType"));
        }});
        logger.info("Maid Notification: " + maid_k);

        r.put("maidTemplate", t);

        return r;
    }

    private Map<String, Object> createMaidWhenRetractCancellationTemplate(Map<String, Object> m) {
        String maidWhenRetractCancellationMessage = (String) m.get("maidWhenRetractCancellationMessage");
        Map<String, Object> r = new HashMap<>();
        Template maidWhenRetractCancellationTemplate = (Template) m.get("maidWhenRetractCancellationTemplate");

        if (maidWhenRetractCancellationTemplate != null) return r;

        String maidRetract_k = (String) m.get("maidRetract_k");

        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(
                maidRetract_k,
                maidWhenRetractCancellationMessage,
                maidWhenRetractCancellationMessage, 2, new HashMap<String, Object>() {{
                    put("target", m.get("maidTarget"));
                    if (m.containsKey("contractType")) put("contractType", m.get("contractType"));
                }});

        r.put("maidWhenRetractCancellationTemplate", t);
        logger.info("Maid Retract Sms: " + maidRetract_k);

        return r;
    }

    public DDMessagingContract getDdMessagingContract(DDMessaging ddMessaging, PicklistItem bank) {
        if (bank == null) return ddMessaging;

        List<DDBankMessaging> l = Setup.getRepository(DDBankMessagingRepository.class)
                .findByDdMessagingAndBank(ddMessaging, bank);
        logger.info("ddMessaging id: " + ddMessaging.getId() +
                "; bank id: " + bank.getId() +
                "; ddBankMessaging id: " + (l.isEmpty() ? "null" : l.get(0).getId()));

        return l.isEmpty() ? ddMessaging : l.get(0);
    }

    public void createAllowedParametersForOldDdMessaging(DDMessagingType event, DDMessagingSubType subType){

        logger.info("event: " + event + "; subEvent: " + subType);
        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", event);
        if (subType != null) q.filterBy("subType", "=", subType);

        List<DDMessaging> l = q.execute();

        List<TemplateChannelParameter> clientParameters = Setup.getApplicationContext()
                .getBean(QueryService.class)
                .getDdMessagingParametersBasedOnEventAndSubEvent(event, subType, "clientTemplate");
        logger.info("clientParameters size: " + clientParameters);

        List<TemplateChannelParameter> maidParameters = Setup.getApplicationContext()
                .getBean(QueryService.class)
                .getDdMessagingParametersBasedOnEventAndSubEvent(event, subType, "maidTemplate");
        logger.info("maidParameters size: " + maidParameters);

        List<TemplateChannelParameter> maidRetractCancellationParameters = Setup.getApplicationContext()
                .getBean(QueryService.class)
                .getDdMessagingParametersBasedOnEventAndSubEvent(event, subType, "maidWhenRetractCancellationTemplate");
        logger.info("maidRetractCancellationParameters size: " + maidRetractCancellationParameters);

        for (DDMessaging d : l) {
            logger.info("DDMessaging id: " + d.getId());
            if (d.getClientTemplate() != null) {
                createAllowedParametersForTemplate(d.getClientTemplate(), clientParameters);
            }

            if (d.getMaidTemplate() != null) {
                createAllowedParametersForTemplate(d.getMaidTemplate(), maidParameters);
            }

            if (d.getMaidWhenRetractCancellationTemplate() != null) {
                createAllowedParametersForTemplate(d.getMaidWhenRetractCancellationTemplate(), maidRetractCancellationParameters);
            }
        }
    }

    public void createAllowedParametersForTemplate(Template t, List<TemplateChannelParameter> parameters) {
        logger.info("Template id: " + t.getId());

        if (parameters.isEmpty()) {
            logger.info("parameters is empty");
            return;
        }

        Set<String> oldAllowedParameters = t
                .getAllowedParameters()
                .stream()
                .map(TemplateAllowedParameter::getName)
                .collect(Collectors.toSet());

        TemplateAllowedParameterRepository templateAllowedParameterRepository =
                Setup.getRepository(TemplateAllowedParameterRepository.class);

        parameters.forEach(p -> {
            logger.info("Template channel parameter id: " + p.getId() + "; name: " + p.getName());
            if (oldAllowedParameters.contains(p.getName())) return;

            logger.info("Not found before -> create a new one: " + p.getId());
            TemplateAllowedParameter allowedParameter = new TemplateAllowedParameter();
            allowedParameter.setName(p.getName());
            allowedParameter.setExpression(p.isExpression());
            allowedParameter.setValue(p.getValue());
            allowedParameter.setTemplate(t);
            templateAllowedParameterRepository.save(allowedParameter);
            oldAllowedParameters.add(p.getName());
        });
    }

    public void addCreditCardOfferParameterAcc6624_DdBankMessages() {
        List<DDBankMessaging> l;
        Long lastId = -1L;

        do {
            SelectQuery<DDBankMessaging> q = new SelectQuery<>(DDBankMessaging.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("ddMessaging.scheduleTermCategory", "=", DirectDebitMessagingScheduleTermCategory.None);
            q.filterBy("ddMessaging.event", "in", Arrays.asList(
                    DDMessagingType.DirectDebitRejected,
                    DDMessagingType.IncompleteDDRejectedByDataEntry,
                    DDMessagingType.IncompleteDDClientHasNoApprovedSignature));
            q.setLimit(200);
            l = q.execute();

            l.forEach(d -> {

                if (d.getClientTemplate() == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS) == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification) == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@sms_cc_offer_sentence@") ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains("@cc_offer_sentence@"))
                    return;

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        d.getClientTemplate().getName(),
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText() + "@cc_offer_sentence@",
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText() + "@sms_cc_offer_sentence@",
                        0);
            });

            if (!l.isEmpty()) {
                lastId = l.get(l.size() - 1).getId();
            }

        } while (!l.isEmpty());
    }

    public void addCreditCardOfferParameterAcc6624() {
        List<DDMessaging> l;
        Long lastId = -1L;

        do {
            SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
            q.filterBy("id", ">", lastId);
            q.filterBy("scheduleTermCategory", "=", DirectDebitMessagingScheduleTermCategory.None);
            q.filterBy("event", "in", Arrays.asList(
                    DDMessagingType.DirectDebitRejected,
                    DDMessagingType.IncompleteDDRejectedByDataEntry,
                    DDMessagingType.IncompleteDDClientHasNoApprovedSignature));
            q.setLimit(200);
            l = q.execute();

            l.forEach(d -> {

                if (d.getClientTemplate() == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS) == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification) == null ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText().contains("@sms_cc_offer_sentence@") ||
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText().contains("@cc_offer_sentence@"))
                    return;

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        d.getClientTemplate().getName(),
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.Notification).getText() + "@cc_offer_sentence@",
                        d.getClientTemplate().getChannelSetting(ChannelSpecificSettingType.SMS).getText() + "@sms_cc_offer_sentence@",
                        0);
            });

            if (!l.isEmpty()) {
                lastId = l.get(l.size() - 1).getId();
            }

        } while (!l.isEmpty());
    }

    public void updateNotificationsAcc6668ReadSheetData(MultipartFile file, String sheetName) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return ;
        }

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue;

            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());
                long ddId = (long) row.getCell(0).getNumericCellValue();

                if (ddId == -1) break;
                String notificationText = row.getCell(1).getStringCellValue();
                String smsText = row.getCell(2).getStringCellValue();
                String maidText = row.getCell(3).getStringCellValue();
                int priority = (int) row.getCell(4).getNumericCellValue();

                DDMessaging dd = ddMessagingRepository.findOne(ddId);
                Map<String, Object> m = getUpdateTemplateMap(notificationText, smsText);

                if (!notificationText.trim().equals("-")) {
                    m.put("notificationCanClosedByUser", false);
                    accountingTemplateService.createOrUpdateNewModelTemplate(
                            dd.getClientTemplate().getName(), notificationText, smsText, priority, m);

                }

                if (!maidText.trim().equals("-")) {
                    String templateName = Boolean.TRUE.equals(dd.getSendToMaidWhenRetractCancellation()) ?
                            dd.getMaidWhenRetractCancellationTemplate() == null ? null : dd.getMaidWhenRetractCancellationTemplate().getName() :
                            dd.getMaidTemplate() == null ? null : dd.getMaidTemplate().getName();

                    if (templateName == null) continue;
                    accountingTemplateService.createOrUpdateNewModelTemplate(
                            templateName, maidText, maidText, 0, new HashMap<>());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private Map<String, Object> getUpdateTemplateMap(String notificationText, String smsText) {
        Map<String, String> expressionParameters = new HashMap<>();

        if ((notificationText != null &&
                notificationText.contains("@worker_type@")) ||
                (smsText != null &&
                        smsText.contains("@worker_type@"))) {
            expressionParameters.put("worker_type", "#root == null ? " +
                    "\"maid\" : " +
                    "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                    "workerType != null ? " +
                    "workerType.getCode().equals(\"private_driver\") ? " +
                    "\"driver\" : " +
                    "\"maid\" : " +
                    "\"maid\" : " +
                    "\"maid\"");
        }

        if ((notificationText != null &&
                notificationText.contains("@her_his@")) ||
                (smsText != null &&
                        smsText.contains("@her_his@"))) {
            expressionParameters.put("her_his", "#root == null ? " +
                    "\"her\" : " +
                    "#root instanceof T(com.magnamedia.entity.Contract) ? " +
                    "housemaid == null ? " +
                    "\"her\" : " +
                    "housemaid.getGender() != null ? " +
                    "housemaid.getGender().equals(T(com.magnamedia.extra.Gender).Female) ? " +
                    "\"her\" : " +
                    "\"his\" : " +
                    "\"her\" : " +
                    "\"her\"");
        }


        return new HashMap<String, Object>(){{
            put("expressionParameters", expressionParameters);
        }};
    }

    public void updateNotificationsAcc6795ReadSheet(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return;
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "<>", DDMessagingType.Termination);
        query.filterBy("scheduleTermCategory", "<>", DirectDebitMessagingScheduleTermCategory.None);
        query.filterBy("sendToMaidWhenRetractCancellation", "<>", true);

        boolean firstTime = false;
        List<DDMessaging> l = query.execute();
        if (!l.isEmpty()) {
            l.forEach(d -> {
                d.setDeleted(true);
            });
            ddMessagingRepository.save(l);
            firstTime = true;
        }

        query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.Termination);
        l = query.execute();

        for (int i = 1; i <=4; i++) {

            try {
                logger.info("Row Num: " + i);

                Row row = sheet.getRow(i);
                String nText = row.getCell(0).getStringCellValue();
                String smsText = row.getCell(1).getStringCellValue();
                String maidText = row.getCell(2).getStringCellValue();
                String contractProspectTypes = row.getCell(3).getStringCellValue().equals("CC") ?
                        "maids.cc_prospect" : "maidvisa.ae_prospect";
                DirectDebitMessagingScheduleTermCategory category = row.getCell(4).getStringCellValue().equalsIgnoreCase("today") ?
                        DirectDebitMessagingScheduleTermCategory.EToday : DirectDebitMessagingScheduleTermCategory.GToday;
                int priority = (int) row.getCell(5).getNumericCellValue();


                if (firstTime) {
                    createDDMessagingAcc6795(nText, smsText, maidText, priority,
                            contractProspectTypes, category);
                } else {
                    l.stream().filter(d -> d.getContractProspectTypes().equals(contractProspectTypes) &&
                                    d.getScheduleTermCategory().equals(category))
                            .findFirst()
                            .ifPresent(d -> {
                                accountingTemplateService.createOrUpdateNewModelTemplate(
                                        d.getClientTemplate().getName(), nText, smsText, priority, new HashMap<>());

                                accountingTemplateService.createOrUpdateNewModelTemplate(
                                        d.getMaidTemplate().getName(), maidText, maidText, 0, new HashMap<>());
                            });

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void createDDMessagingAcc6795(
            String notification, String sms, String maidText, int priority,
            String contractProspectTypes, DirectDebitMessagingScheduleTermCategory category) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.Termination);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setScheduleTermCategory(category);
        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        ddMessaging.setSendToMaid(true);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.Termination);
        List<DDMessaging> ddMessagingList = query.execute();

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        Map<String, Object> m = new HashMap<>();
        m.put("notificationCanClosedByUser", true);


        m.put("contractType", contractProspectTypes.equals("maids.cc_prospect") ? "maids.cc" : "maidvisa");
        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_notification_" + DDMessagingType.Termination + "_",
                oldClientDdMessagingCount);
        Template t = accountingTemplateService
                .createNewModelTemplateAndAddAllowedParameters(k, notification, sms, priority, m);
        ddMessaging.setClientTemplate(t);

        long oldMaidDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getMaidTemplate() != null)
                .count() + 1;
        String maid_k = fetchTemplateName(
                "Accounting_dd_messaging_setup_maid_notification_" + DDMessagingType.Termination + "_",
                oldMaidDdMessagingCount);
        Template maidTemplate = accountingTemplateService
                .createNewModelTemplateAndAddAllowedParameters(maid_k, maidText, maidText, 0, new HashMap<String, Object>() {{
                    put("target", PicklistHelper.getItem("template_target", "Housemaids"));
                    put("contractType", m.get("contractType"));
                }});
        ddMessaging.setMaidTemplate(maidTemplate);

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public static DDMessaging getDdMessaging(
            SelectQuery<DDMessaging> q,
            Contract c,
            DirectDebitMessagingScheduleTermCategory scheduleTermCategory) {
        return getDdMessaging(q, c, scheduleTermCategory, false);
    }

    public static DDMessaging getDdMessaging(
            SelectQuery<DDMessaging> q,
            Contract c,
            DirectDebitMessagingScheduleTermCategory scheduleTermCategory,
            boolean sendPayTabsWithTermination) {

        List<DDMessaging> l = q.execute();
        if (!l.isEmpty()) return l.get(0);

        if (scheduleTermCategory.equals(DirectDebitMessagingScheduleTermCategory.None)) return null;

        q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.Termination);
        q.filterBy("contractProspectTypes", "like", "%" + c.getContractProspectType().getCode() + "%");
        q.filterBy("scheduleTermCategory", "=", scheduleTermCategory);

        // ACC-8949
        if (sendPayTabsWithTermination) {
            q.filterBy("sendPayTabMessage", "=", true);
        }

        l = q.execute();
        return l.isEmpty() ? null : l.get(0);
    }

    public void updateNotificationsAcc6804ReadSheetData(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet not found");
            return ;
        }

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.IncompleteDDClientHasNoApprovedSignature);

        List<DDMessaging> l = q.execute();

        if (l.stream().mapToInt(d -> Integer.parseInt(d.getReminders())).min().getAsInt() == 0) {
            l.forEach(d -> {
                d.setReminders(String.valueOf(Integer.parseInt(d.getReminders()) + 1));
                d.setSubType(DDMessagingSubType.MISSING_BANK_INFO);
            });

            ddMessagingRepository.save(l);
        }

        Map<String, Object> m = new HashMap<String, Object>() {{
            put("notificationCanClosedByUser", false);
        }};

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue;

            try {
                logger.log(Level.INFO, "Row Num = {0}", row.getRowNum());
                int reminder = (int) row.getCell(0).getNumericCellValue();

                if (reminder == -1) break;
                String notificationText = row.getCell(1).getStringCellValue();
                String smsText = row.getCell(2).getStringCellValue();
                int priority = (int) row.getCell(3).getNumericCellValue();

                DDMessaging dd = l.stream()
                        .filter(d -> d.getReminders().equals(String.valueOf(reminder)))
                        .findFirst()
                        .orElse(null);

                if (dd == null) return;

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        dd.getClientTemplate().getName(), notificationText, smsText, priority, m);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void updateNotificationsAcc6563(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);

        if (sheet == null) {
            logger.warning("Sheet not found");
            return;
        }

        Row row = sheet.getRow(1);
        String query = row.getCell(0).getStringCellValue();
        List<Template> l = new ArrayList<>();
        EntityManager em = Setup.getEntityManagerFactory().createEntityManager();
        try {
            Query resultQuery = em.createNativeQuery(query);
            List<Object> ids = resultQuery.getResultList();
            l = templateRepository.findAll(
                    ids.stream().map(o -> ((BigInteger) o).longValue() ).collect(Collectors.toList()));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            em.close();
        }

        if (l.isEmpty()) return;

        for (Template t : l) {
            Map<String, String> newAllowedParameters = new HashMap<>();
            for (ChannelSpecificSetting c : t.getChannelSpecificSettings()) {
                logger.info("Template id: " + t.getId() +
                        "; Template name: " + t.getName() +
                        "; channel type: " + c.getType() +
                        "; channel text: " + c.getText());

                if (c.getText().contains("@Client_Name@")) {
                    c.setText(c.getText().replace("@Client_Name@", "@client_nickname_or_name@"));
                    if (!newAllowedParameters.containsKey("Client_Name"))
                        newAllowedParameters.put("Client_Name", "client_nickname_or_name");
                }

                if (c.getText().contains("@client_name@")) {
                    c.setText(c.getText().replace("@client_name@", "@client_nickname_or_name@"));
                    if (!newAllowedParameters.containsKey("client_name"))
                        newAllowedParameters.put("client_name", "client_nickname_or_name");
                }

                if (c.getText().contains("@dear_receiver_name@")) {
                    c.setText(c.getText().replace("@dear_receiver_name@", "Dear @client_nickname_or_first_name@"));
                    if (!newAllowedParameters.containsKey("dear_receiver_name"))
                        newAllowedParameters.put("dear_receiver_name", "client_nickname_or_first_name");
                }

                if (c.getText().contains("@client_first_name@")) {
                    c.setText(c.getText().replace("@client_first_name@", "@client_nickname_or_first_name@"));
                    if (!newAllowedParameters.containsKey("client_first_name"))
                        newAllowedParameters.put("client_first_name", "client_nickname_or_first_name");
                }

                if (c.getText().contains("@client_first_name_with_title@")) {
                    c.setText(c.getText().replace("@client_first_name_with_title@", "@client_nickname_or_first_name@"));
                    if (!newAllowedParameters.containsKey("client_first_name_with_title"))
                        newAllowedParameters.put("client_first_name_with_title", "client_nickname_or_first_name");
                }
                logger.info("Template id: " + t.getId() +
                        "; Template name: " + t.getName() +
                        "; channel type: " + c.getType() +
                        "; channel new text: " + c.getText());
            }

            TemplateUtil.updateChannelSpecificSettings(t, t.getChannelSpecificSettings());
            //replaceAllowedParameters(t, newAllowedParameters);
        }
    }

    private void replaceAllowedParameters(Template t, Map<String, String> newAllowedParameters) {
        if (newAllowedParameters.isEmpty()) return;
        TemplateAllowedParameterRepository templateAllowedParameterRepository =
                Setup.getRepository(TemplateAllowedParameterRepository.class);
        /*Set<String> oldParaSet = new HashSet<>();

        newAllowedParameters.forEach((oldPara, newPara) -> {
            if (oldParaSet.contains(oldPara)) return;
            oldParaSet.add(oldPara);
            TemplateAllowedParameter a = t.getAllowedParameterValue(oldPara);
            if (a == null) return;
            logger.info("Template id: " + t.getId() +
                    "; delete old allowed parameter: " + oldPara);
            templateAllowedParameterRepository.delete(a);
        });*/

        Set<String> newParaSet = new HashSet<>();
        newAllowedParameters.forEach((oldPara, newPara) -> {
            if (newParaSet.contains(newPara)) return;
            newParaSet.add(newPara);
            logger.info("Template id: " + t.getId() +
                    "; add new allowed parameter: " + newPara);
            TemplateAllowedParameter a = new TemplateAllowedParameter();
            a.setName(newPara);
            a.setTemplate(t);
            templateAllowedParameterRepository.saveAndIgnoreDuplication(a);
        });
    }

    public void updateClientViaCCMessagesAcc6840(
            MultipartFile file, String sheetName, List<DDMessaging> l, boolean isFirstTime) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (sheet == null) {
            logger.log(Level.WARNING, "Sheet: " + sheetName + " not found");
            return;
        }

        DDMessagingSubType subType = null;

        switch (sheetName) {
            case "Insufficient Funds":
                subType = DDMessagingSubType.INSUFFICIENT_FUNDS;
                break;
            case "Expiring Card":
                subType = DDMessagingSubType.EXPIRED_CARD;
                break;
            case "Card or Account Issue":
                subType = DDMessagingSubType.ACCOUNT_ISSUE;
                break;
            case "Exceeds Card Limit":
                subType = DDMessagingSubType.EXCEEDING_DAILY_LIMITS;
                break;
            case "Token Deleted":
                subType = DDMessagingSubType.TOKEN_DELETED;
                break;
            case "OTHERs":
                subType = DDMessagingSubType.OTHER_ISSUES;
                break;
        }

        if (subType == null) return;

        logger.info("Sheet: " + sheetName + " ; subType: " + subType);

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            if (row.getCell(0) == null) return;

            logger.info("Row Num: " + row.getRowNum());

            int trial = (int) row.getCell(0).getNumericCellValue();

            String n = row.getCell(1).getStringCellValue().trim();
            String sms = row.getCell(2).getStringCellValue().trim();
            String contractType = row.getCell(3).getStringCellValue().equals("CC") ?
                    "maids.cc_prospect" :
                    "maidvisa.ae_prospect";
            final DDMessagingSubType finalsubType=subType;
            DDMessaging ddMessaging = l.stream()
                    .filter(d -> d.getSubType().equals(finalsubType) &&
                            d.getTrials().equals(String.valueOf(trial)) &&
                            d.getContractProspectTypes().equals(contractType))
                    .findFirst().orElse(null);
            Map<String, Object> m = getUpdateTemplateMap(n, sms);
            m.put("notificationCanClosedByUser", false);
            if (!isFirstTime) {
                if (ddMessaging == null) throw new BusinessException("DD messaging not found");
                accountingTemplateService.createOrUpdateNewModelTemplate(ddMessaging.getClientTemplate().getName(),
                        n, sms, 2, m);
            }else {
                createDDMessagingAcc6840(n, sms, 2, contractType, subType, trial);
            }
        }
    }


    @Transactional
    public void handleTrialBasedSheet(
            MultipartFile file, String sheetName,
            DDMessagingSubType subType, List<DDMessaging> existingMessages, String contractType) throws IOException {

        logger.info("sheet name: " + sheetName +
                "; subType: " + subType +
                "; existing messages size: " + existingMessages.size());

        boolean isFirstTime = existingMessages.size() == 3;
        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheet(sheetName);

        if (isFirstTime) {
            existingMessages.forEach(d -> {
                if (d.getTrials().equals("1")) return;
                if (d.getTrials().equals("2")) {
                    d.setTrials("4");
                }
                if (d.getTrials().equals("3")) {
                    d.setTrials("7");
                }
                ddMessagingRepository.save(d);
            });
        }

        for (int i = 2; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null || row.getCell(1) == null || row.getCell(1).toString().isEmpty()) continue;

            try {
                int trial = (int) row.getCell(0).getNumericCellValue();
                String sms = row.getCell(1).getStringCellValue();
                DDMessaging d = existingMessages.stream().filter(dd -> dd.getTrials().equals(String.valueOf(trial)))
                        .findFirst().orElse(null);

                if (d != null) {
                    logger.info("update existing message: " + d.getId());
                    accountingTemplateService.createOrUpdateNewModelTemplate(
                            d.getClientTemplate().getName(), null, sms, 2, new HashMap<>());
                } else {

                    logger.info("create new message for subtype: " + subType +
                            "; trial: " + trial +
                            "; sms: " + sms );

                    createDDMessagingAcc6840(null, sms, 2, contractType, subType, trial);
                }
            } catch (Exception ex) {
                logger.log(Level.WARNING, "Error processing row " + i + " in sheet " + sheet.getSheetName(), ex);
            }
        }
    }

    private void createDDMessagingAcc6840(
            String notification, String sms, int priority,
            String contractProspectTypes, DDMessagingSubType subType,
            int trial) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.ClientsPayingViaCreditCard);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setClientMessage(notification);
        ddMessaging.setClientSmsMessage(sms);
        ddMessaging.setReminders(String.valueOf(1));
        ddMessaging.setTrials(String.valueOf(trial));
        ddMessaging.setSubType(subType);
        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ClientsPayingViaCreditCard);

        List<DDMessaging> ddMessagingList = query.execute();

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        Map<String, Object> m = getUpdateTemplateMap(notification, sms);

        m.put("contractType", contractProspectTypes.equals("maids.cc_prospect") ? "maids.cc" : "maidvisa");
        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_notification_" + DDMessagingType.ClientsPayingViaCreditCard + "_",
                oldClientDdMessagingCount);
        Template t = accountingTemplateService
                .createNewModelTemplateAndAddAllowedParameters(k, null, sms, priority, m);
        ddMessaging.setClientTemplate(t);
        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public boolean validateCreateClientToDo(DDMessaging entity) {
        return !entity.isCreateClientToDo() || ClientToDoService.ClientTodoFlow.getFlowName(entity.getEvent()) != null;
    }

    public void createTerminationMessagesAcc8949(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);
        if (sheet == null) {
            logger.info("The Sheet is not found");
            return;
        }

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.Termination);
        q.filterBy("sendPayTabMessage", "=", true);
        List<DDMessaging> l = q.execute();
        logger.info("DDMessaging size: " + l.size());

        for (Row row : sheet) {

            if (row.getRowNum() == 0) continue;
            if (row.getCell(0).getNumericCellValue() == -1) return;
            logger.info("Row Num: " + row.getRowNum());

            String contractType = row.getCell(1).getStringCellValue().equals("CC") ?
                    AccountingModule.MAID_CC_PROSPECT_TYPE_CODE :
                    AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE;

            DirectDebitMessagingScheduleTermCategory category = row.getCell(2).getStringCellValue().equalsIgnoreCase("today") ?
                    DirectDebitMessagingScheduleTermCategory.EToday :
                    DirectDebitMessagingScheduleTermCategory.GToday;

            int priority = (int) row.getCell(3).getNumericCellValue();

            String nClient = row.getCell(4).getStringCellValue().trim();
            String smsClient = row.getCell(5).getStringCellValue().trim();
            String smsMaid = row.getCell(6).getStringCellValue().trim();

            List<DDMessaging> ddMessagings = l.stream()
                    .filter(d -> d.getScheduleTermCategory() != null &&
                            d.getScheduleTermCategory().equals(category) &&
                            d.getContractProspectTypes().equals(contractType))
                    .collect(Collectors.toList());

            if (ddMessagings.isEmpty()) {
                createDDMessagingAcc8949(nClient, smsClient, smsMaid, priority, contractType, category);
            } else {
                DDMessaging ddMessaging = ddMessagings.get(0);
                if (ddMessaging == null) throw new BusinessException("DD messaging not found");

                Map<String, Object> m = getUpdateTemplateMap(nClient, smsClient);
                m.put("notificationCanClosedByUser", false);

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        ddMessaging.getClientTemplate().getName(), nClient, smsClient, priority, m);

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        ddMessaging.getMaidTemplate().getName(), smsMaid, smsMaid, priority, new HashMap<>());
            }
        }
    }

    private void createDDMessagingAcc8949(
            String notificationClient, String smsClient, String smsMaid, int priority,
            String contractProspectTypes, DirectDebitMessagingScheduleTermCategory category) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.Termination);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setScheduleTermCategory(category);
        ddMessaging.setClientMessagePriority(ClientMessagePriority.SEND_SMS_WITH_NOTIFICATION);
        ddMessaging.setSendToMaid(true);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setSendPayTabMessage(true);
        ddMessaging.setClientMessage(notificationClient);
        ddMessaging.setClientSmsMessage(smsClient);
        ddMessaging.setMaidMessage(smsMaid);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.Termination);
        List<DDMessaging> ddMessagingList = query.execute();

        // Client Template
        Map<String, Object> m = new HashMap<>();
        m.put("notificationCanClosedByUser", true);
        m.put("contractType", contractProspectTypes.equals("maids.cc_prospect") ? "maids.cc" : "maidvisa");

        long oldClientDdMessagingCount = ddMessagingList.stream().filter(dd -> dd.getClientTemplate() != null).count();
        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_notification_" + DDMessagingType.Termination + "_",
                ++oldClientDdMessagingCount);
        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(k, notificationClient, smsClient, priority, m);
        ddMessaging.setClientTemplate(t);

        // Maid Template
        long oldMaidDdMessagingCount = ddMessagingList.stream().filter(dd -> dd.getMaidTemplate() != null).count();
        String maid_k = fetchTemplateName(
                "Accounting_dd_messaging_setup_maid_notification_" + DDMessagingType.Termination + "_",
                ++oldMaidDdMessagingCount);
        Template maidTemplate = accountingTemplateService
                .createNewModelTemplateAndAddAllowedParameters(maid_k, smsMaid, smsMaid, 0, new HashMap<String, Object>() {{
                    put("target", PicklistHelper.getItem("template_target", "Housemaids"));
                    put("contractType", m.get("contractType"));
                }});
        ddMessaging.setMaidTemplate(maidTemplate);

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public void createExtensionFlowMessagesAcc8954(MultipartFile file) throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);
        if (sheet == null) {
            logger.info("The Sheet is not found");
            return;
        }

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", DDMessagingType.ExtensionFlow);
        List<DDMessaging> l = q.execute();
        logger.info("DDMessaging size: " + l.size());

        for (Row row : sheet) {
            Map<String, Object> m = new HashMap<>();

            if (row.getRowNum() == 0) continue;
            if (row.getCell(0).getNumericCellValue() == -1) return;
            logger.info("Row Num: " + row.getRowNum());

            String contractType = AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE;
            int trial = (int) row.getCell(1).getNumericCellValue();
            int reminder = (int) row.getCell(2).getNumericCellValue();

            String sms = "";
            int priority = -1;
            String isRobotCall = row.getCell(4).getStringCellValue();
            if (Boolean.parseBoolean(isRobotCall)) {
                priority = (int) row.getCell(5).getNumericCellValue();
                m.put("maxRetries", (int) row.getCell(6).getNumericCellValue());
                m.put("retryDelay", (long) row.getCell(7).getNumericCellValue());
                m.put("isRobotCall", isRobotCall);
            } else {
                sms = row.getCell(3).getStringCellValue().trim();
            }

            m.put("method", row.getCell(8) != null ? row.getCell(8).getStringCellValue() : "sms");

            if (l.isEmpty()) {
                createDDMessagingAcc8954(sms, priority, contractType, trial, reminder, m);
            } else {
                DDMessaging ddMessaging = l.stream()
                        .filter(d -> d.getContractProspectTypes().equals(contractType) &&
                                d.getTrials().equals(String.valueOf(trial)) &&
                                d.getReminders().equals(String.valueOf(reminder)))
                        .findFirst()
                        .orElse(null);
                if (ddMessaging == null) throw new BusinessException("DD messaging not found");

                accountingTemplateService.createOrUpdateNewModelTemplate(
                        ddMessaging.getClientTemplate().getName(), null, sms, priority, m);
            }
        }
    }

    private void createDDMessagingAcc8954(
            String sms, int priority,
            String contractProspectTypes,
            int trial, int reminder, Map<String, Object> m) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setEvent(DDMessagingType.ExtensionFlow);
        ddMessaging.setSubType(DDMessagingSubType.PAYMENT_REMINDER_EXTENSION);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setReminders(String.valueOf(reminder));
        ddMessaging.setTrials(String.valueOf(trial));
        if (sms != null) {
            ddMessaging.setClientSmsMessage(sms);
        }

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.ExtensionFlow);
        List<DDMessaging> ddMessagingList = query.execute();

        m.put("contractType", contractProspectTypes.equals(AccountingModule.MAID_CC_PROSPECT_TYPE_CODE) ? "maids.cc" : "maidvisa");

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_notification_" + DDMessagingType.ExtensionFlow + "_",
                oldClientDdMessagingCount);
        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(k, null, sms, priority, m);
        ddMessaging.setClientTemplate(t);

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public void updateReminderFlowMessagesAcc9286(XSSFWorkbook workbook, String sheetName, DDMessagingPaymentStructure paymentStructure) {

        XSSFSheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
            logger.info("Sheet: " + sheetName + " not found");
            return;
        }

        DDMessagingType ddMessagingType = null;
        List<DDMessagingSubType> ddMessagingSubTypes = null;
        switch (sheetName) {
            case "Multiple Required Flow":
            case "One Payment Required Flow":
                ddMessagingType = DDMessagingType.OnlineCreditCardPaymentReminders;
                ddMessagingSubTypes = Collections.singletonList(DDMessagingSubType.PAYMENT_REMINDER_FOR_REQUIRED_PAYMENTS);
                break;
            case "Multiple Non Required Flow":
            case "One Payment Non Required Flow":
                ddMessagingType = DDMessagingType.OnlineCreditCardPaymentReminders;
                ddMessagingSubTypes = Collections.singletonList(DDMessagingSubType.PENDING_PAYMENT);
                break;
            case "Multiple Initial Flow":
            case "One Payment Initial Flow":
                ddMessagingType = DDMessagingType.ClientsPayingViaCreditCard;
                ddMessagingSubTypes = Arrays.asList(DDMessagingSubType.INITIAL_FLOW_FOR_DDA, DDMessagingSubType.INITIAL_FLOW_FOR_DDB);
                break;
        }

        if (ddMessagingType == null || ddMessagingSubTypes == null) return;
        logger.info("Sheet: " + sheetName + "; ddMessagingType: " + ddMessagingType + "; ddMessagingSubTypes: " + ddMessagingSubTypes);

        SelectQuery<DDMessaging> q = new SelectQuery<>(DDMessaging.class);
        q.filterBy("event", "=", ddMessagingType);
        q.filterBy("subType", "in", ddMessagingSubTypes);

        List<DDMessaging> l = q.execute();
        l.forEach(ddMessaging -> ddMessaging.setDeleted(true));

        boolean isFirstTime = !l.isEmpty() && l.stream().allMatch(ddMessaging -> ddMessaging.getPaymentStructure() == null);

        if (isFirstTime) {
            if (DDMessagingPaymentStructure.MULTIPLE_PAYMENTS.equals(paymentStructure)) {
                l = new ArrayList<>();
            }
        } else {
            l = l.stream()
                    .filter(ddMessaging -> paymentStructure.equals(ddMessaging.getPaymentStructure()))
                    .collect(Collectors.toList());
        }

        for (Row row : sheet) {
            if (row.getRowNum() == 0) continue;
            logger.info("Row Num: " + row.getRowNum());

            int rowIndex = (int) row.getCell(0).getNumericCellValue();
            if (rowIndex == -1) break;

            int trial = (int) row.getCell(1).getNumericCellValue();
            int reminder = (int) row.getCell(2).getNumericCellValue();
            String contractType = row.getCell(3).getStringCellValue().equals("CC") ?
                    AccountingModule.MAID_CC_PROSPECT_TYPE_CODE : AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE;
            String sms = row.getCell(4).getStringCellValue();

            for (DDMessagingSubType ddMessagingSubType : ddMessagingSubTypes) {
                handleDDMessagingAcc9286(l, isFirstTime, sms, ddMessagingType, ddMessagingSubType, paymentStructure, reminder, trial, contractType);
            }
        }

        // Delete any old messages that are no longer in use.
       List<DDMessaging> deletedDdMessagings = l.stream()
                .filter(DDMessaging::getDeleted)
                .collect(Collectors.toList());
        logger.info("deleted DD Messaging size: " + deletedDdMessagings.stream().map(DDMessaging::getId).collect(Collectors.toList()));
        if (deletedDdMessagings.isEmpty()) return;
        ddMessagingRepository.saveAll(deletedDdMessagings);
    }

    private void handleDDMessagingAcc9286(
            List<DDMessaging> l, boolean isFirstTime, String sms, DDMessagingType ddMessagingType, DDMessagingSubType ddMessagingSubType,
            DDMessagingPaymentStructure paymentStructure, int reminder, int trial, String contractType) {

        List<DDMessaging> ddMessagings = l.stream()
                .filter(d -> d.getSubType().equals(ddMessagingSubType) &&
                        (isFirstTime || d.getPaymentStructure().equals(paymentStructure)) &&
                        d.getReminders().equals(String.valueOf(reminder)) &&
                        d.getTrials().equals(String.valueOf(trial)) &&
                        d.getContractProspectTypes().equals(contractType))
                .collect(Collectors.toList());

        logger.info("DD Messaging id: " + ddMessagings.stream().map(DDMessaging::getId).collect(Collectors.toList()));
        if (ddMessagings.isEmpty()) {
            createDDMessagingAcc9286(
                    sms, contractType, ddMessagingType, ddMessagingSubType,
                    paymentStructure, trial, reminder, new HashMap<>());
        } else {
            DDMessaging ddMessaging = ddMessagings.get(0);
            if (ddMessaging == null) throw new BusinessException("DD messaging not found");
            accountingTemplateService.createOrUpdateNewModelTemplate(ddMessaging.getClientTemplate().getName(),
                    null, sms, 0, new HashMap<>());

            ddMessaging.setDeleted(false);
            if (isFirstTime) {
                // Used old DD Messaging with add Payment Structure
                ddMessaging.setPaymentStructure(paymentStructure);
                ddMessagingRepository.save(ddMessaging);
                logger.info("Update paymentStructure DD Messaging id: " + ddMessaging);
            }
        }
    }

    private void createDDMessagingAcc9286(
            String sms, String contractProspectTypes,
            DDMessagingType ddMessagingType, DDMessagingSubType ddMessagingSubType,
            DDMessagingPaymentStructure paymentStructure,
            int trial, int reminder, Map<String, Object> m) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setSendToClient(true);
        ddMessaging.setEvent(ddMessagingType);
        ddMessaging.setSubType(ddMessagingSubType);
        ddMessaging.setPaymentStructure(paymentStructure);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setReminders(String.valueOf(reminder));
        ddMessaging.setTrials(String.valueOf(trial));
        ddMessaging.setClientSmsMessage(sms);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", ddMessagingType);
        List<DDMessaging> ddMessagingList = query.execute();
        m.put("contractType", contractProspectTypes.equals(AccountingModule.MAID_CC_PROSPECT_TYPE_CODE) ? "maids.cc" : "maidvisa");

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;

        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_notification_" + ddMessagingType + "_",
                oldClientDdMessagingCount);
        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(k, null, sms, 0, m);
        ddMessaging.setClientTemplate(t);

        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public static void additionalFilter(SelectQuery<DDMessaging> query, DDMessagingType event, ContractPaymentTerm cpt) {

        SelectFilter selectFilter = getFilterByFlowType(event, cpt);
        if (selectFilter == null) return;

        query.filterBy(selectFilter);
    }

    public static SelectFilter getFilterByFlowType(DDMessagingType event, ContractPaymentTerm cpt) {

        switch (event) {
            case DirectDebitRejected:
               /* boolean isMV_SDR = cpt.getContract().isMaidVisa() &&
                        Setup.getApplicationContext()
                                .getBean(PaymentService.class)
                                .hasRequiredPaymentSDRNotPaid(cpt);
                return isMV_SDR ?
                        new SelectFilter("flowType", "=", DDMessagingContractType.MAIDVISA_SDR) :
                        new SelectFilter("flowType", "<>", DDMessagingContractType.MAIDVISA_SDR);*/
        }

        return null;
    }

    public boolean shouldDelayTerminationMessageBeforePaidEndDate(DDMessagingType event, DDMessaging ddMessaging, ContractPaymentTerm cpt, boolean isMV_SDR ) {

        if (!ddMessaging.getEvent().equals(DDMessagingType.Termination)) return false;

        // For Rejection Flow
        if (DDMessagingType.DirectDebitRejected.equals(event) && isMV_SDR) return false;


        return new LocalDate(cpt.getContract().getPaidEndDate()).isAfter(new LocalDate());
    }

    public void updateRejectionFlowMessagesAcc8851(XSSFWorkbook workbook, List<DDMessaging> l, String sheetName) {

        XSSFSheet sheet = workbook.getSheet(sheetName);
        if (sheet == null) {
            logger.info("Sheet: " + sheetName + " not found");
            return;
        }

        DirectDebitRejectCategory rejectCategory;
        switch (sheetName) {
            case "Signature":
                rejectCategory = DirectDebitRejectCategory.Signature;
                break;
            case "Account":
                rejectCategory = DirectDebitRejectCategory.Account;
                break;
            case "Invalid Account":
                rejectCategory = DirectDebitRejectCategory.Invalid_Account;
                break;
            case "EID":
                rejectCategory = DirectDebitRejectCategory.EID;
                break;
            case "Authorization":
                rejectCategory = DirectDebitRejectCategory.Authorization;
                break;
            default:
                return;
        }

        List <DDMessaging> ddMessagingList =  l.stream()
                .filter(d -> d.getRejectCategory().equals(rejectCategory))
                .collect(Collectors.toList());
        logger.info("DDMessaging size: " + ddMessagingList.size());

        for (Row row : sheet) {
            Map<String, Object> m = new HashMap<>();

            if (row.getRowNum() == 0) continue;
            logger.info("Row Num: " + row.getRowNum());

            // 1-Extract the Data from the Sheet
            if ((int) row.getCell(0).getNumericCellValue() == -1) break;

            int trial = (int) row.getCell(1).getNumericCellValue();
            int reminder = (int) row.getCell(2).getNumericCellValue();
            String contractType = row.getCell(3).getStringCellValue().equals("CC") ?
                    AccountingModule.MAID_CC_PROSPECT_TYPE_CODE :
                    AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE;

            String smsMV_SDR = null;
            if (contractType.equals(AccountingModule.MAID_VISA_PROSPECT_TYPE_CODE)) {
                smsMV_SDR = row.getCell(4).getStringCellValue();
            }

            String sms = row.getCell(5).getStringCellValue();

            // 2-Handle DD Messaging
            List<DDMessaging> ddMessagings = ddMessagingList.stream()
                    .filter(d -> d.getContractProspectTypes().equals(contractType) &&
                            d.getTrials().equals(String.valueOf(trial)) &&
                            d.getReminders().equals(String.valueOf(reminder)))
                    .collect(Collectors.toList());

            logger.info("DD Messaging " +
                    "contractType: " + contractType +
                    "; trial: " + trial +
                    "; reminder: " + reminder +
                    "; ids: " + ddMessagings.stream().map(DDMessaging::getId).collect(Collectors.toList()));
            int priority = -1;
            if (ddMessagings.isEmpty()) {
                createDDMessagingAcc8851(sms, priority, contractType, rejectCategory, trial, reminder, m);
            } else {
                // Get default dd message
                accountingTemplateService.createOrUpdateNewModelTemplate(
                        ddMessagings.get(0).getClientTemplate().getName(), null, sms, priority, m);
                ddMessagings.get(0).setDeleted(false);
            }

            // Handle dd message related to MaidVisa SDR
            if (smsMV_SDR != null) {
                ddMessagings = ddMessagingList.stream()
                        .filter(d -> d.getContractProspectTypes().equals(DDMessagingContractType.MAID_VISA_SDR.getLabel()) &&
                                d.getTrials().equals(String.valueOf(trial)) &&
                                d.getReminders().equals(String.valueOf(reminder)))
                        .collect(Collectors.toList());

                if (ddMessagings.isEmpty()) {
                    createDDMessagingAcc8851(
                            smsMV_SDR, priority, DDMessagingContractType.MAID_VISA_SDR.getLabel(),
                            rejectCategory, trial, reminder, m);
                } else {
                    accountingTemplateService.createOrUpdateNewModelTemplate(
                            ddMessagings.get(0).getClientTemplate().getName(), null, smsMV_SDR, priority, m);
                    ddMessagings.get(0).setDeleted(false);
                }
            }
        }
    }

    private void createDDMessagingAcc8851(
            String sms, int priority, String contractProspectTypes,
            DirectDebitRejectCategory directDebitRejectCategory,
            int trial, int reminder, Map<String, Object> m) {

        DDMessaging ddMessaging = new DDMessaging();
        ddMessaging.setSendToClient(true);
        ddMessaging.setSendToMaid(false);
        ddMessaging.setSendAsEmail(false);
        ddMessaging.setScheduleTermCategory(DirectDebitMessagingScheduleTermCategory.None);
        ddMessaging.setClientSmsMessage(sms);
        ddMessaging.setReminders(String.valueOf(reminder));
        ddMessaging.setTrials(String.valueOf(trial));
        ddMessaging.setEvent(DDMessagingType.DirectDebitRejected);
        ddMessaging.setRejectCategory(directDebitRejectCategory);
        ddMessaging.setContractProspectTypes(contractProspectTypes);
        ddMessaging.setDdCategory("A,B");


        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.DirectDebitRejected);
        List<DDMessaging> ddMessagingList = query.execute();

        m.put("contractType", contractProspectTypes.equals(AccountingModule.MAID_CC_PROSPECT_TYPE_CODE) ?
                "maids.cc" : "maidvisa");

        long oldClientDdMessagingCount = ddMessagingList.stream()
                .filter(dd -> dd.getClientTemplate() != null)
                .count() + 1;
        String k = fetchTemplateName(
                "Accounting_dd_messaging_setup_client_" + DDMessagingType.DirectDebitRejected + "_",
                oldClientDdMessagingCount);
        Template t = accountingTemplateService.createNewModelTemplateAndAddAllowedParameters(k, null, sms, priority, m);
        ddMessaging.setClientTemplate(t);
        ddMessagingRepository.saveAndFlush(ddMessaging);
    }

    public void updateCollectionLogUponSendingNewMessage(FlowProcessorEntity entity) {
        if (!entity.getFlowEventConfig().getName().equals(FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card)) return;
        logger.info("flow id: " + entity.getId());

        String flowType = entity.getContract().isOneMonthAgreement() ?
                CollectionFlowLogController.PICKLISTITEM_ONE_MONTH_AGREEMENT_CODE :
                CollectionFlowLogController.PICKLISTITEM_CLIENT_PAYING_VIA_CREDIT_CARD_FLOW_CODE;
        CollectionFlowLog l = collectionFlowLogRepository.findTopByFlowType_CodeAndContractAndEndedFalse(flowType, entity.getContract());

        if (l == null) {
            collectionFlowLogService.generateCollectionFlowLog(flowType,
                    entity.getContract(), entity.getContract().getId(),
                    entity.getContract().getEntityType(), new Date(), new Date(),
                    entity.getContract().isOneMonthAgreement() ?
                            collectionFlowLogService.getOneMonthAgreementLogStatus(entity.getContract()) :
                            collectionFlowLogService.getPayingViaCreditCardLogStatus(entity.getContract()),
                    "",
                    entity.getId(),
                    entity.getEntityType());

            return;
        }

        if (entity.getId().equals(l.getCurrentRunningFlowId())) return;

        l.setCurrentRunningFlowId(entity.getId());
        l.setCurrentRunningFlowEntity(entity.getEntityType());
        collectionFlowLogRepository.save(l);
    }

}