package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.User;
import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.CooExpenseNotification;
import com.magnamedia.entity.ExpenseNotification;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.MaintenanceRequestType;
import com.magnamedia.repository.CooExpenseNotificationRepository;
import com.magnamedia.repository.ExpensePaymentRepository;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.workflow.type.PurchasingToDoType;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Service
public class ExpenseNotificationService {
    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;

    @Autowired
    private CooExpenseNotificationRepository cooExpenseNotificationRepository;

    @Autowired
    private EmailTemplateService emailTemplateService;

    @Autowired
    private UserRepository userRepository;

    protected static final Logger logger = Logger.getLogger(ExpenseNotificationService.class.getName());


    public void sendNotifications(ExpenseRequestTodo expenseRequestTodo) {
        ExpenseNotification notificationInfo = expenseRequestTodo.getExpense().getNotification();
        if (notificationInfo == null)
            return;
        String reason = "";
        if (notificationInfo.isAmountEnabled()) {
            boolean sendNotification = false;
            switch (notificationInfo.getAmountOperation()) {
                case EQIAL_TO: {
                    if (expenseRequestTodo.getAmount().equals(notificationInfo.getAmount())) {
                        sendNotification = true;
                        reason = "Amount = " + notificationInfo.getAmount();
                    }
                    break;
                }
                case LESS_THAN: {
                    if (expenseRequestTodo.getAmount() < notificationInfo.getAmount()) {
                        sendNotification = true;
                        reason = "Amount < " + notificationInfo.getAmount();
                    }
                    break;
                }
                case GREATER_THAN: {
                    if (expenseRequestTodo.getAmount() > notificationInfo.getAmount()) {
                        sendNotification = true;
                        reason = "Amount > " + notificationInfo.getAmount();
                    }
                    break;
                }
                case LESS_THAN_OR_EQIAL_TO: {
                    if (expenseRequestTodo.getAmount() <= notificationInfo.getAmount()) {
                        sendNotification = true;
                        reason = "Amount <= " + notificationInfo.getAmount();
                    }
                    break;
                }
                case GREATER_THAN_OR_EQIAL_TO: {
                    if (expenseRequestTodo.getAmount() >= notificationInfo.getAmount()) {
                        sendNotification = true;
                        reason = "Amount >= " + notificationInfo.getAmount();
                    }
                    break;
                }
            }
            if (sendNotification) {
                List<User> usersToSendMail = notificationInfo.getNotifyUsers().stream()
                        .filter(user -> !user.hasPosition(AccountingModule.EXPENSE_COO_USER_POSITION))
                        .collect(Collectors.toList());
                if (!usersToSendMail.isEmpty()) {
                    emailTemplateService.sendExpenseRequestTodoEmail(usersToSendMail, null, "expense_request_notification_amount_limit", expenseRequestTodo);
                }

                CooExpenseNotification cooExpenseNotification = new CooExpenseNotification();
                cooExpenseNotification.setText(emailTemplateService.fillTemplate("expense_request_notification_amount_limit", expenseRequestTodo));
                cooExpenseNotification.setExpenseRequestTodo(expenseRequestTodo);
                cooExpenseNotification.setReason(reason);
                cooExpenseNotificationRepository.save(cooExpenseNotification);
            }

        }
        if (notificationInfo.isPercentageEnabled()) {
            boolean sendNotification = false;
            Double average = 0D;
            String percentageComparedTo = "";
            switch (notificationInfo.getPercentageComparedTo()) {
                case MONTH: {
                    percentageComparedTo = " month";
                    Date now = new Date();
                    Date monthStart = new LocalDate(DateUtil.addMonths(now, -1 * notificationInfo.getPercentageNOCompared())).withDayOfMonth(1).toDate();
                    List<ExpenseRequestTodo> expenseRequestTodos = expenseRequestTodoRepository.findByExpenseAndCreationDateBetweenOrderByCreationDateAsc(expenseRequestTodo.getExpense(), monthStart, now);
                    expenseRequestTodos.forEach(ex -> logger.info("EX#" + ex.getId() + ", Amount: " + ex.getAmount()));
                    if (expenseRequestTodos != null && !expenseRequestTodos.isEmpty()) {
                        average = !expenseRequestTodos.isEmpty() ? expenseRequestTodos.stream().mapToDouble(todo -> todo.getAmount()).average().getAsDouble() : 0D;
                    }
                    break;
                }
                case PAYMENT: {
                    percentageComparedTo = " expense";
                    List<Long> toExcludeIds = Arrays.asList(expenseRequestTodo.getExpensePayment() != null && expenseRequestTodo.getExpensePayment().getId() != null ?
                            expenseRequestTodo.getExpensePayment().getId() : -1L);

                    List<ExpensePayment> expensePayments = expensePaymentRepository.findByExpenseToPostAndIdNotInOrderByCreationDateDesc(expenseRequestTodo.getExpenseToPost() != null ? expenseRequestTodo.getExpenseToPost() : expenseRequestTodo.getExpense(), toExcludeIds);

                    if (expensePayments.size() > notificationInfo.getPercentageNOCompared() && !notificationInfo.getPercentageNOCompared().equals(0)) {
                        expensePayments = expensePayments.subList(0, notificationInfo.getPercentageNOCompared());
                    }
                    expensePayments.forEach(ep -> logger.info("EP#" + ep.getId() + ", Amount: " + ep.getAmount()));
                    average = !expensePayments.isEmpty() ? expensePayments.stream().mapToDouble(todo -> todo.getAmount()).average().getAsDouble() : 0D;

                    break;
                }
            }
            logger.info("Average: " + average);
            Double avaragePrecentage = (notificationInfo.getPercentage() * average) / 100;
            String operation = "";
            switch (notificationInfo.getPercentageOperation()) {
                case EQIAL_TO: {
                    operation = "= ";
                    if (expenseRequestTodo.getAmount().equals(avaragePrecentage)) {
                        sendNotification = true;
                    }
                    break;
                }
                case LESS_THAN: {
                    operation = "< than ";
                    if (expenseRequestTodo.getAmount() < avaragePrecentage)
                        sendNotification = true;
                    break;
                }
                case GREATER_THAN: {
                    operation = "> than ";
                    if (expenseRequestTodo.getAmount() > avaragePrecentage)
                        sendNotification = true;
                    break;
                }
                case LESS_THAN_OR_EQIAL_TO: {
                    operation = "<= than ";
                    if (expenseRequestTodo.getAmount() <= avaragePrecentage)
                        sendNotification = true;
                    break;
                }
                case GREATER_THAN_OR_EQIAL_TO: {
                    operation = ">= than ";
                    if (expenseRequestTodo.getAmount() >= avaragePrecentage)
                        sendNotification = true;
                    break;
                }
            }
            if (sendNotification) {
                emailTemplateService.sendExpenseRequestTodoEmail(notificationInfo.getNotifyUsers(), null, "expense_request_notification_amount_percentage", expenseRequestTodo);
                CooExpenseNotification cooExpenseNotification = new CooExpenseNotification();
                cooExpenseNotification.setText(emailTemplateService.fillTemplate("expense_request_notification_amount_percentage", expenseRequestTodo));
                cooExpenseNotification.setExpenseRequestTodo(expenseRequestTodo);
                reason = "Amount " + operation + notificationInfo.getPercentage() + "% of the average of last " + notificationInfo.getPercentageNOCompared() + percentageComparedTo + (notificationInfo.getPercentageNOCompared() > 1 ? "s" : "");
                cooExpenseNotification.setReason(reason);
                cooExpenseNotificationRepository.save(cooExpenseNotification);
            }

        }

        if (notificationInfo.isNumberOfRequestsEnabled()) {
            Date now = new Date();
            Date startDate = null;
            String comparedTo = "";
            switch (notificationInfo.getNumberOfRquestComparedTo()) {
                case MONTH: {
                    comparedTo = "month";
                    startDate = new LocalDate(now).withDayOfMonth(1).toDate();
                    break;
                }
                case DAY: {
                    comparedTo = "day";
                    startDate = DateUtil.getDayStart(now);
                    break;
                }
                case WEEK: {
                    comparedTo = "week";
                    startDate = DateUtil.getStartOfCurrentWeek();
                    break;
                }
                case YEAR: {
                    comparedTo = "year";
                    startDate = DateUtil.getStartOfCurrentYear();
                    break;
                }
            }
            int threshold = Integer.valueOf(Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_EXPENSE_NOTIFICATION_NUMBER_OF_REQUESTS_THRESHOLD));
            List<ExpenseRequestTodo> expenseRequestTodos = expenseRequestTodoRepository.findByExpenseAndCreationDateBetweenOrderByCreationDateAsc(expenseRequestTodo.getExpense(), startDate, now);
            if (expenseRequestTodos.size() > threshold) {
                emailTemplateService.sendExpenseRequestTodoEmail(notificationInfo.getNotifyUsers(), null, "expense_request_notification_amount_repeated", expenseRequestTodo);

                CooExpenseNotification cooExpenseNotification = new CooExpenseNotification();
                cooExpenseNotification.setText(emailTemplateService.fillTemplate("expense_request_notification_amount_repeated", expenseRequestTodo));
                cooExpenseNotification.setExpenseRequestTodo(expenseRequestTodo);
                reason = "Expense requested more than +" + threshold + (threshold > 1 ? "times" : "time") + " in a " + comparedTo;
                cooExpenseNotification.setReason(reason);
                cooExpenseNotificationRepository.save(cooExpenseNotification);
            }


        }


    }
    //ACC-3907
    public void expenseToDoCreatedEmail(String todo_type, String taskName) {
        logger.log(Level.INFO, "todo_type " + todo_type);
        logger.log(Level.INFO, "taskName " + taskName);


        Map<String, String> parameters = new HashMap();
        parameters.put("todo_type", todo_type);
        String baseURL = Setup.getParameter(Setup.getCurrentModule(), AccountingModule.PARAMETER_FRONT_END_URL) + "#!/accounting/";
        String recipientsEmailCode = "";
        switch (todo_type) {
            case "PayrollAccountantTodo":
                parameters.put("related_screen_name", "Bank transfers");
                parameters.put("link_of_the_screen", baseURL + "accountant-todo");
                recipientsEmailCode = AccountingModule.BANK_TRANSFER_PAGE_NOTIFICATION_RECIPIENTS;
                break;
            case "PurchasingToDo":
                if (Arrays.asList(PurchasingToDoType.PA_CONFIRM_PURCHASING_REQUEST.toString(),
                        PurchasingToDoType.PA_CONFIRM_BEST_SUPPLIER.toString()).contains(taskName)) {
                    parameters.put("related_screen_name", "Purchase Auditor");
                    parameters.put("link_of_the_screen", baseURL + "purchase-requests");
                    recipientsEmailCode = AccountingModule.PURCHASE_AUDITOR_PAGE_NOTIFICATION_RECIPIENTS;
                } else if (Arrays.asList(PurchasingToDoType.PM_GET_BEST_SUPPLIER.toString(),
                    PurchasingToDoType.PM_PURCHASE.toString(),
                    PurchasingToDoType.PM_GET_BETTER_SUPPLIER.toString()).contains(taskName)) {
                    parameters.put("related_screen_name", "Purchase Manager");
                    parameters.put("link_of_the_screen", baseURL + "purchase-manager");
                    recipientsEmailCode = AccountingModule.PURCHASE_MANAGER_PAGE_NOTIFICATION_RECIPIENTS;
                }
                break;
            case "MaintenanceRequest":
                if (MaintenanceRequestType.PURCHASE_AUDITOR_APPROVE_REQUEST.toString().equals(taskName)) {
                    parameters.put("related_screen_name", "Purchase Auditor");
                    parameters.put("link_of_the_screen", baseURL + "purchase-requests");
                    recipientsEmailCode = AccountingModule.PURCHASE_AUDITOR_PAGE_NOTIFICATION_RECIPIENTS;
                } else if (Arrays.asList(MaintenanceRequestType.PURCHASE_MANAGER_GET_BETTER_PRICE.toString(),
                        MaintenanceRequestType.PURCHASE_MANAGER_GET_PRICE.toString()).contains(taskName)) {
                    parameters.put("related_screen_name", "Purchase Manager");
                    parameters.put("link_of_the_screen", baseURL + "purchase-manager");
                    recipientsEmailCode = AccountingModule.PURCHASE_MANAGER_PAGE_NOTIFICATION_RECIPIENTS;
                }
                break;
            case "BucketReplenishmentTodo":
                parameters.put("related_screen_name", "Audit Manager");
                parameters.put("link_of_the_screen", baseURL + "audit-manager");
                recipientsEmailCode = AccountingModule.AUDIT_MANAGER_PAGE_NOTIFICATION_RECIPIENTS;
                break;
            case "ExpenseRequestTodo":
                parameters.put("related_screen_name", "Credit Card Holder");
                parameters.put("link_of_the_screen", baseURL + "credit-card-holder");
                recipientsEmailCode = AccountingModule.CREDIT_CARD_HOLDER_PAGE_NOTIFICATION_RECIPIENTS;
                break;
        }

        if (parameters.size() == 3) {
            logger.log(Level.INFO, "Start send the emails");

            Setup.getApplicationContext()
                    .getBean(MessagingService.class)
                    .sendEmailToOfficeStaff("expense_todo_creation",
                            parameters, Setup.getParameter(Setup.getCurrentModule(), recipientsEmailCode),
                            todo_type + " TO-DO Notification");
        }

    }

    public void sendExpenseRejectedApprovedRequestEmail(
            ExpenseRequestTodo todo,
            String notes,
            String action) {

        logger.log(Level.INFO, "todo id " + todo.getId());
        logger.log(Level.INFO, "rejectionNotes " + notes);
        String subject = (todo.getExpense() != null ? todo.getExpense().getName() : "")
                + " - " + action + " - " + new DateTime(todo.getCreationDate()).toString("yyyy-MM-dd HH:mm:ss");

        String approverName = "";
        User u = CurrentRequest.getUser();

        if (u.getLoginName() != null && !u.getLoginName().equals("guest")) {
            approverName = u.getName();

        } else if (todo.getApproveHolderEmail() != null) {
            SelectQuery<User> q = new SelectQuery<>(User.class);
            q.filterBy("email", "=", todo.getApproveHolderEmail().toLowerCase()).isIgnoreCase();
            q.filterBy("activated", "=", true);
            q.setLimit(1);

            List<User> l = q.execute();
            approverName = l.isEmpty() ? todo.getApproveHolderEmail() : l.get(0).getName();
        }

        String recordInfo = "Amount: " + todo.getAmount() + "<br>";
        recordInfo += "Description: " + todo.getDescription() + "<br>";
        recordInfo += (todo.getRequestedBy() != null ?  "Requested by: " + todo.getRequestedBy() : "Requested by: ERP");

        Map<String, String> params = new HashMap();
        params.put("record_info", recordInfo);
        params.put("approver_name", approverName);
        params.put("rejection_or_approval", (action.equals("Approved") ? "approval" : "rejection"));
        params.put("notes", notes);
        emailTemplateService.sendExpenseRequestTodoEmail(todo.getRequestedBy().getEmail(), subject, "expense_request_rejected_approved", params);

    }

}
