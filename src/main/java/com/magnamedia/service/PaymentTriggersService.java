package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.type.BusinessEvent;
import com.magnamedia.entity.Payment;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.PaymentRepository;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR> Mahfoud
 */
@Service
public class PaymentTriggersService {
    private static final Logger logger = Logger.getLogger(PaymentTriggersService.class.getName());

    public void apply(Payment entity, BusinessEvent event) {
        if (entity.isDeepSilentSave()) return;

        switch (event) {
            case AfterCreate:
                afterInsert(entity);
                break;
            case AfterUpdate:
                afterUpdate(entity);
                break;
        }
    }

    private void afterInsert(Payment entity) {
        updateTravelAssistPayment(entity, BusinessEvent.AfterCreate);
    }

    private void afterUpdate(Payment entity) {
        updateTravelAssistPayment(entity, BusinessEvent.AfterUpdate);
    }

    private void updateTravelAssistPayment(Payment entity, BusinessEvent event) {
        if (entity.getTypeOfPayment() == null || !"travel_assist".equals(entity.getTypeOfPayment().getCode())) {
            return;
        }

        if (!Arrays.asList(
                PaymentStatus.PRE_PDP,
                PaymentStatus.PDC,
                PaymentStatus.RECEIVED,
                PaymentStatus.DELETED).contains(entity.getStatus())) return;

        if (!event.isCreateEvent()) {
            Payment oldPayment = Setup.getRepository(PaymentRepository.class).findOldPayment(entity.getId());
            if (oldPayment != null && oldPayment.getStatus().equals(entity.getStatus())) return;
        }

        logger.info("Contract Id: " + entity.getContract().getId() + "; Payment Id : " + entity.getId());

        Setup.getApplicationContext()
                .getBean(InterModuleConnector.class)
                .postJsonAsync("/sales/contract/updateContractAgreementTerms/" + entity.getContract().getId(), new HashMap<>());
    }
}