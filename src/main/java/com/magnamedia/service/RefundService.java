package com.magnamedia.service;

import com.magnamedia.controller.DDMessagingToDoController;
import com.magnamedia.controller.DirectDebitCancelationToDoController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.repository.PicklistItemRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Payment;
import com.magnamedia.extra.PaymentHelper;
import com.magnamedia.helper.DateUtil;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.ContractFeesType;
import com.magnamedia.module.type.PaymentStatus;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.PaymentRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR> Hachem
 */

@Service
public class RefundService {
    
    private final Logger logger = Logger.getLogger(RefundService.class.getName());
    
    private static final String prefix = "MMM ";
    
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private PaymentRepository paymentRepository;
    
    @Autowired
    private ClientMessagingAndRefundService clientMessagingAndRefundService;
    @Autowired
    private PaymentService paymentService;
    @Autowired
    private RejectionToDoService rejectionToDoService;

    public Map doCancellationRefundFlow(Contract contract, Client client) {
        logger.log(Level.SEVERE, "contract id: {0}", contract.getId());
        Map<String, Object> map = new HashMap();
        //if (!Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED).contains(contract.getStatus())) return map;
        
        // revised flow
        Double correctedBalance = Setup.getApplicationContext().getBean(ContractService.class)
                .getCorrectedBalance(contract.getId(), DateUtil.formatDateDashed(contract.getDateOfTermination() != null
                        ? contract.getDateOfTermination() : contract.getScheduledDateOfTermination()));

        // we don't owes refund to the client
        if (correctedBalance != null && correctedBalance >= 0D) return map;

        // we owes refund to the client
        PicklistItem monthlyPayment = Setup.getItem(
                AccountingModule.PICKLIST_PAYMETN_TYPE_OF_PAYMENT_CODE,
                "monthly_payment");

        // Fetch RECEIVED Monthly payments ordered by Payment Date DESC
        SelectQuery<Payment> selectQuery = new SelectQuery(Payment.class);
        selectQuery.filterBy("contract", "=", contract)
                .and("status", "=", PaymentStatus.RECEIVED)
                .and("typeOfPayment", "=", monthlyPayment);
        selectQuery.sortBy("isProRated", true, false);
        selectQuery.sortBy("dateOfPayment", false, true);
        List<Payment> payments = selectQuery.execute();
        Double clientBalance = Math.abs(correctedBalance);

        Double amountToRefund = 0.0;
        for (Payment payment : payments) {
            // ACC-4060
            PicklistItem paymentType = Setup.getRepository(PicklistItemRepository.class).findOne(payment.getTypeOfPayment().getId());
            if (paymentType.hasTag("refund")) continue;
            
            //Jirra ACC-2463
            int monthNum = new DateTime(payment.getDateOfPayment()).getMonthOfYear();
            Double dayFee = payment.getAmountOfPayment() / 30.4;
            Double margin = Double.parseDouble(Setup.getParameter(Setup.getCurrentModule(),
                    monthNum == 2 ? AccountingModule.PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_FEBRUARY
                            : AccountingModule.PARAMETER_DIFFERENCE_PERIOD_FOR_RECEIVED_FULL_PAYMENT_ALL_MONTHS)) * dayFee;

            if (!payment.getRefundSentToExpensify()) {
                amountToRefund += paymentRefund(payment, clientBalance, contract, client, margin);
            }
            // if we have refunded the client all what we owe him -> break;
            if (payment.getAmountOfPayment() >= clientBalance - margin) {
                map.put("settled", true);
                break;
            }

            clientBalance -= payment.getAmountOfPayment();
        }
        // ACC-5977
        if (amountToRefund > 0.0)
            clientMessagingAndRefundService.sendSms812ToClient(amountToRefund, contract, false);
        return map;
    }

    private Double paymentRefund(Payment payment, Double clientBalance, Contract contract, Client client, Double margin) {
        // Payment Amount > Remaining Balance + 1 -> partial refund
        if ((payment.getAmountOfPayment() > (clientBalance + margin))) {
            logger.log(Level.SEVERE, prefix + "client balance: " + clientBalance + " payment amount: " + payment.getAmountOfPayment() + " -> partial refund");

            //Jirra ACC-2025
            Contract persistedContract = contractRepository.findOne(contract.getId());
            if (payment.getIsProRated() != null && payment.getIsProRated() && persistedContract.getProRatedPlusMonth() != null && persistedContract.getProRatedPlusMonth()) {
                logger.log(Level.SEVERE, prefix + "PRO + 1 Month Payment -> Full Refund");
                return doFullRefund(contract, client, payment, clientBalance);
            } else {

                if (!contract.getContractFeesType().equals(ContractFeesType.NO_ADJUSTED_END_DATE)) {
                    Map map = clientMessagingAndRefundService.partialRefundProcess(contract, client, clientBalance, payment.getClientInformedAboutRefund(), payment);
                    payment.setRefundSentToExpensify((Boolean) map.get("refundSentToExpensify"));
                    payment.setFullyRefunded(false);
                    payment.setClientInformedAboutRefund((Boolean) map.get("clientInformedAboutRefund"));
                    paymentService.updatePaymentDeepSilentSave(payment);
                } else {
                    logger.log(Level.SEVERE, prefix + "Indefinite CC contract -> don't do partial refund");
                }
            }
        } else {
            // Payment Amount < Remaining Balance + 1 -> full refund
            logger.log(Level.SEVERE, prefix + "client balance: " + clientBalance + " payment amount: " + payment.getAmountOfPayment() + " -> full refund");
            return doFullRefund(contract, client, payment, payment.getAmountOfPayment());
        }
        return 0.0;
    }
    
    public Map<String, Object> doWithinXDaysCancellationFlow(Contract contract, boolean afterTerminationOnly) {
        Map<String, Object> map = new HashMap<>();
        map.put("cancelledWithinFirstXDays", Boolean.TRUE);

        Contract old = contractRepository.findOne(contract.getId());
        rejectionToDoService.stopDDRejectionToDos(old);

        List<Payment> monthlyPayments = paymentRepository.findActiveMonthlyPaymentByContract(contract);
        List<Payment> payments;

        if(afterTerminationOnly) {
            payments = monthlyPayments.stream().filter(p -> {
                LocalDate t = new LocalDate(contract.getDateOfTermination()).withDayOfMonth(1);
                LocalDate d = new LocalDate(p.getDateOfPayment());

                return d.toDate().getTime() >= t.toDate().getTime();
            }).collect(Collectors.toList());
        } else {
            payments = monthlyPayments;
        }

        boolean paymentSentToBank = false;
        Double amountToRefund = 0.0;
        for (Payment p : payments) {
            logger.info("Monthly Payment#" + p.getId());
            
            if (PaymentStatus.RECEIVED.equals(p.getStatus())) {
                logger.info("Fully Refunding");
                // removed code to check if refund payment because limiting type to monthly payment

                amountToRefund += doFullRefund(contract, old.getClient(), p, p.getAmountOfPayment());
            } else if (p.getSentToBankByMDD()) {
                paymentSentToBank = true;
            }
        }

        // ACC-5977
        if (amountToRefund > 0.0) {
            clientMessagingAndRefundService.sendSms812ToClient(amountToRefund, contract, false);
        }

        if (!paymentSentToBank) {
            //ACC-3920
            logger.info("Cancelling bounced payments");
            monthlyPayments.stream()
                    .filter(x -> PaymentStatus.BOUNCED.equals(x.getStatus()) && !x.isReplaced())
                    .forEach(paymentService::deleteBouncedPayment);

            Setup.getApplicationContext().getBean(DirectDebitCancelationToDoController.class)
                    .cancelAllContractDDs(contract);
        }
        
        logger.log(Level.SEVERE, "MaidCCContractCancellationBusinessRule execute Stop message");
        Setup.getApplicationContext().getBean(DDMessagingToDoController.class)
                .closeRelatedToDos(old.getUuid());

        //ACC-3920 remove code set CPT inactive
        return map;
    }

    private Double doFullRefund(
            Contract contract, Client client, Payment payment, Double amount) {

        Double amountToRefund = PaymentHelper.doFullRefund(contract, client, payment, amount, true);
        paymentService.updatePaymentDeepSilentSave(payment);
        return amountToRefund;
    }
}