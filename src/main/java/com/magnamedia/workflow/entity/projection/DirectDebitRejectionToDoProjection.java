package com.magnamedia.workflow.entity.projection;

import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.workflow.Task;
import org.springframework.data.rest.core.config.Projection;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 19, 2019
 * Jirra ACC-1134
 */
@Projection(name = "directDebitRejectionToDoProjection",
        types = BaseEntity.class)
public interface DirectDebitRejectionToDoProjection
        extends Task{

    Long getId();
}
