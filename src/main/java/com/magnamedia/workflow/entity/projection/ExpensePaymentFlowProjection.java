package com.magnamedia.workflow.entity.projection;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.entity.BaseEntity;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.serialize.IdJsonSerializer;
import com.magnamedia.core.serialize.IdLabelSerializer;
import com.magnamedia.core.workflow.Task;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Complaint;
import com.magnamedia.entity.ComplaintType;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.serializer.ClientRefundToDoSerializer;
import com.magnamedia.entity.workflow.ClientRefundToDo;
import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.PaymentRequestPurpose;
import com.magnamedia.workflow.type.ClientRefundPaymentMethod;
import com.magnamedia.workflow.type.ClientRefundRequestType;
import com.magnamedia.workflow.type.ClientRefundStatus;
import com.magnamedia.workflow.type.ClientRefundTodoManagerAction;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.rest.core.config.Projection;

import java.sql.Date;
import java.util.List;

/**
 * Mohammad Nosairat (Jan 20, 2021)
 */
@Projection(types = ExpensePayment.class)
public interface ExpensePaymentFlowProjection extends Task {

    Long getId();

    Double getAmount();

    List<Attachment> getAttachments();
}
