package com.magnamedia.workflow.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.helper.SearchField;
import com.magnamedia.core.workflow.RoleBasedWorkflow;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.workflow.service.directdebitcancelationtodotsteps.DirectDebitCancelationPrepare;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
@Service(value = "directDebitCancelationToDoFlow")
public class DirectDebitCancelationToDoFlow
        extends RoleBasedWorkflow<DirectDebitCancelationToDo>{
    
    @Autowired
    private DirectDebitCancelationPrepare directDebitCancelationPrepare;
    
    @Autowired
    public DirectDebitCancelationToDoFlow(
            DirectDebitCancelationPrepare directDebitCancelationPrepare) {

        setId("directDebitCancelationToDoFlow");
        this.directDebitCancelationPrepare = directDebitCancelationPrepare;
        
        this.getWorkflowSteps().addAll(
                Arrays.asList(
                        this.directDebitCancelationPrepare));
        this.getWorkflowSteps().forEach((workflowStep) -> {
            this.idStepMap.put(workflowStep.getId(), workflowStep);
        });
    }

    @Override
    public List<SearchField> fillSearchFields() {

        List<SearchField> fields = new ArrayList<>();
        SearchField field = new SearchField();
        field.setLabel("Family Name");
        field.setName("directDebitFile.directDebit.contractPaymentTerm.contract.client.name");
        fields.add(field);

        SearchField field1 = new SearchField();
        field1.setLabel("Direct Debit reference number");
        field1.setName("directDebitFile.ddaRefNo");
        fields.add(field1);

        return fields;
    }

    @Override
    public Map<String, String> getTableHeader() {

        Map<String, String> tableHeader = new HashMap<>();
        tableHeader.put("directDebitFile.directDebit.contractPaymentTerm.contract.client.name", "Client Name");
        tableHeader.put("directDebitFile.ddaRefNo", "Direct Debit Reference Number");
        return tableHeader;
    }

    @Override
    public Map<String, String> getTableColumnTypes() {

        Map<String, String> tableColumnTypes = new HashMap<>();
        tableColumnTypes.put("directDebitFile.directDebit.contractPaymentTerm.contract.client.name", "String");
        tableColumnTypes.put("recieverName", "String");
        return tableColumnTypes;
    }

    public List<SearchField> getDefaultSearchableFields() {
        return Arrays.asList(
                new SearchField("creationDate", "Creation Date", "timestamp",
                        Setup.DATE_OPERATIONS),
                new SearchField("lastModificationDate", "Last Update Date", "timestamp",
                        Setup.DATE_OPERATIONS)
        );
    }

    @Override
    public String getWorkflowHeader() {

        return "Direct Debit Cancelation ToDo Flow";
    }
}
