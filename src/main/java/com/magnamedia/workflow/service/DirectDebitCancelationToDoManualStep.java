package com.magnamedia.workflow.service;

import com.magnamedia.core.entity.workflow.WorkflowEntity;
import com.magnamedia.core.repository.workflow.WorkflowRepository;
import com.magnamedia.core.workflow.ManualTask;
import org.springframework.beans.factory.annotation.Autowired;

/**
 *
 * <AUTHOR> kanaan <<EMAIL>>
 * Created on Nov 20, 2019
 * Jirra ACC-1134
 */
public abstract class DirectDebitCancelationToDoManualStep <T extends WorkflowEntity>
        extends ManualTask<T> {

    @Autowired
    private WorkflowRepository<T> repository;

    @Override
    public WorkflowRepository<T> getRepository() {
        return repository;
    }

    @Override
    public void onSave(T entity) {
    }

}
