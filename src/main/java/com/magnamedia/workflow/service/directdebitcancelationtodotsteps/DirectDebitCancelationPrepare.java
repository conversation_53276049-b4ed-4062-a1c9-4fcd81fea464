package com.magnamedia.workflow.service.directdebitcancelationtodotsteps;

import com.magnamedia.core.helper.CurrentRequest;
import com.magnamedia.entity.DirectDebit;
import com.magnamedia.entity.DirectDebitFile;
import com.magnamedia.entity.workflow.DirectDebitCancelationToDo;
import com.magnamedia.module.type.DirectDebitMethod;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.repository.DirectDebitCancelationToDoRepository;
import com.magnamedia.repository.DirectDebitFileRepository;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.workflow.service.DirectDebitCancelationToDoManualStep;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR> kanaan <<EMAIL>>
 *         Created on Nov 20, 2019
 *         Jirra ACC-1134
 */
@Service
public class DirectDebitCancelationPrepare
        extends DirectDebitCancelationToDoManualStep<DirectDebitCancelationToDo> {

    @Autowired
    private DirectDebitCancelationToDoRepository directDebitCancelationToDoRepository;

    @Autowired
    private DirectDebitFileRepository directDebitFileRepository;

    @Autowired
    private DirectDebitRepository directDebitRepository;

    public DirectDebitCancelationPrepare() {
        this.setId("Direct Debit Cancelation Prepare");
    }

    @Override
    public void onSave(DirectDebitCancelationToDo entity) {
    }

    @Override
    public void postSave(DirectDebitCancelationToDo entity) {
    }

    @Override
    public void onDone(DirectDebitCancelationToDo entity) {

        DirectDebitFile ddf = directDebitFileRepository.findOne(entity.getDirectDebitFile().getId());

        //Jirra ACC-2676
        DirectDebit directDebit = ddf.getDirectDebit();
        boolean ddNeedsSave = false;
        if (ddf.getDdStatus().equals(DirectDebitStatus.CONFIRMED)) {
            if (ddf.getDdMethod().equals(DirectDebitMethod.AUTOMATIC)
                    && directDebit.getAutoDdfFile() != null
                    && directDebit.getAutoDdfFile().getId() != null
                    && directDebit.getAutoDdfFile().getId().equals(ddf.getId())) {
                directDebit.setAutoDdfFile(null);
                directDebit.setStatus(DirectDebitStatus.PENDING_FOR_CANCELLATION);
                ddNeedsSave = true;
            }

            if (ddf.getDdMethod().equals(DirectDebitMethod.MANUAL)
                    && directDebit.getManualDdfFile() != null
                    && directDebit.getManualDdfFile().getId() != null
                    && directDebit.getManualDdfFile().getId().equals(ddf.getId())) {
                directDebit.setManualDdfFile(null);
                directDebit.setMStatus(DirectDebitStatus.PENDING_FOR_CANCELLATION);
                ddNeedsSave = true;
            }
        }

        ddf.setDdStatus(DirectDebitStatus.PENDING_FOR_CANCELLATION);
        ddf.setSenderForCancellation(CurrentRequest.getUser().getId());
        ddf.setSendForCancellationDate(new Date());

        directDebitFileRepository.save(ddf);

        if (ddNeedsSave) {
            directDebitRepository.save(directDebit);
        }

        entity.setCompleted(true);
        super.onDone(entity);
    }

    @Override
    public void postDone(DirectDebitCancelationToDo entity) {
    }
}
